import transporter from '../config/email.config.js';
import config from '../config/environment.config.js';
import bookCoachCallTemplate from './template/bookCoachCallTemplate.js';

const sendBookCoachCallEmail = async (user, coachCallRequest) => {
  const emailHtml = bookCoachCallTemplate(user, coachCallRequest);

  await transporter.sendMail({
    from: config.email_from,
    to: user.email,
    subject: 'Coach Call Request',
    html: emailHtml,
  });
};

export default sendBookCoachCallEmail;
