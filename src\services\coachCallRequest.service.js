import sendBookCoachCallEmail from '../mailer/bookCoachCall.js';
import { CoachCallRequest } from '../models/coachCallRequest.js';
import { ValidationError, ForbiddenError } from '../utils/errorHandler.js';
import { ValidationMessages } from '../constants/messages.js';
import { HttpStatus } from '../constants/httpStatus.js';
import { FreeFeatureType } from '../constants/enums.js';
import { FeatureAccessMessages } from '../constants/featureLimits.js';
import UserFeatureUsageModel from '../models/userFeatureUsage.model.js';
import subscriptionModel from '../models/order/subscription.model.js';

class CoachCallRequestService {
  /**
   * Check if user has access to coach call feature
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Access information
   */
  static async checkCoachCallAccess(userId) {
    // Check if user has active subscription
    const activeSubscription = await subscriptionModel.findOne({
      userId,
      isActive: true,
      status: 'active',
      endDate: { $gt: new Date() },
    });

    if (activeSubscription) {
      return {
        hasAccess: true,
        accessType: 'subscription',
        unlimited: true,
        subscription: activeSubscription,
      };
    }

    // Check free usage limits
    const usageInfo = await UserFeatureUsageModel.checkFeatureAccess(userId, FreeFeatureType.FREE_COACH_CALL);

    return {
      hasAccess: usageInfo.canUse,
      accessType: 'free',
      unlimited: false,
      usageInfo,
    };
  }

  /**
   * Get user's coach call usage summary
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Usage summary
   */
  static async getCoachCallUsageSummary(userId) {
    const accessInfo = await this.checkCoachCallAccess(userId);

    if (accessInfo.unlimited) {
      return {
        hasActiveSubscription: true,
        canRequestCall: true,
        unlimited: true,
        usageCount: null,
        usageLimit: null,
        remainingCalls: null,
      };
    }

    const { usageInfo } = accessInfo;
    return {
      hasActiveSubscription: false,
      canRequestCall: usageInfo.canUse,
      unlimited: false,
      usageCount: usageInfo.usageCount,
      usageLimit: usageInfo.usageLimit,
      remainingCalls: usageInfo.remainingUsage,
      lastUsedAt: usageInfo.lastUsedAt,
    };
  }

  static async createCoachCallRequest(req, res) {
    if (!req.body) {
      throw new ValidationError(
        ValidationMessages.GENERAL.INVALID_REQUEST_BODY,
        HttpStatus.STATUS_CODE.UNPROCESSABLE_ENTITY
      );
    }

    const userId = req.user.id || req.user._id;
    const fullName = `${req.user.firstName} ${req.user.lastName}`;
    const email = req.user.email;

    const { userMessage, preferredDate, preferredTime } = req.body;

    if (!preferredDate || !preferredTime) {
      throw new ValidationError('User preferred date, and preferred time are required fields');
    }

    // Check if user has access to coach call feature
    const accessInfo = await this.checkCoachCallAccess(userId);

    if (!accessInfo.hasAccess) {
      if (accessInfo.accessType === 'free') {
        throw new ForbiddenError(
          FeatureAccessMessages.getFeatureLimitMessage(FreeFeatureType.FREE_COACH_CALL, accessInfo.usageInfo.usageLimit)
        );
      } else {
        throw new ForbiddenError(FeatureAccessMessages.SUBSCRIPTION_REQUIRED);
      }
    }

    // Create the coach call request
    const newCoachCallRequest = new CoachCallRequest({
      userName: fullName,
      userEmail: email,
      userMessage,
      preferredDate,
      preferredTime,
    });

    await newCoachCallRequest.save();

    // Record feature usage if it's a free user
    if (!accessInfo.unlimited) {
      await UserFeatureUsageModel.recordUsage(userId, FreeFeatureType.FREE_COACH_CALL, {
        entityType: 'CoachCallRequest',
        entityId: newCoachCallRequest._id,
      });
    }

    const formattedRequest = {
      id: newCoachCallRequest._id,
      userName: newCoachCallRequest.userName,
      userEmail: newCoachCallRequest.userEmail,
      userMessage: newCoachCallRequest.userMessage,
      preferredDate: newCoachCallRequest.preferredDate,
      preferredTime: newCoachCallRequest.preferredTime,
      status: newCoachCallRequest.status,
      accessType: accessInfo.accessType,
      unlimited: accessInfo.unlimited,
    };

    // Send email notification
    sendBookCoachCallEmail(req.user, formattedRequest);

    return formattedRequest;
  }

  /**
   * Get all coach call requests for a user
   * @param {Object} req - Express request object
   * @returns {Promise<Array>} Array of coach call requests
   */
  static async getUserCoachCallRequests(req) {
    const userId = req.user.id || req.user._id;
    const email = req.user.email;

    const requests = await CoachCallRequest.find({ userEmail: email }).sort({ createdAt: -1 });

    // Get usage summary
    const usageSummary = await this.getCoachCallUsageSummary(userId);

    return {
      requests: requests.map((request) => ({
        id: request._id,
        userName: request.userName,
        userEmail: request.userEmail,
        userMessage: request.userMessage,
        preferredDate: request.preferredDate,
        preferredTime: request.preferredTime,
        status: request.status,
        createdAt: request.createdAt,
        updatedAt: request.updatedAt,
      })),
      usageSummary,
    };
  }

  /**
   * Cancel a coach call request
   * @param {Object} req - Express request object
   * @returns {Promise<Object>} Updated request
   */
  static async cancelCoachCallRequest(req) {
    const { requestId } = req.params;
    const userId = req.user.id || req.user._id;
    const email = req.user.email;

    const request = await CoachCallRequest.findOne({
      _id: requestId,
      userEmail: email,
    });

    if (!request) {
      throw new ValidationError('Coach call request not found or you do not have permission to cancel it');
    }

    if (request.status === 'completed' || request.status === 'cancelled') {
      throw new ValidationError(`Cannot cancel a request that is already ${request.status}`);
    }

    request.status = 'cancelled';
    await request.save();

    return {
      id: request._id,
      status: request.status,
      message: 'Coach call request cancelled successfully',
    };
  }
}

export default CoachCallRequestService;
