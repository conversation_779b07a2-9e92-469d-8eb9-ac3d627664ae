import sendBookCoachCallEmail from '../mailer/bookCoachCall.js';
import { CoachCallRequest } from '../models/coachCallRequest.js';
import { ValidationError } from '../utils/errorHandler.js';

class CoachCallRequestService {
  static async createCoachCallRequest(req, res) {
    if (!req.body) {
      throw new ValidationError(
        ValidationMessages.GENERAL.INVALID_REQUEST_BODY,
        HttpStatus.STATUS_CODE.UNPROCESSABLE_ENTITY
      );
    }

    const fullName = `${req.user.firstName} ${req.user.lastName}`;
    const email = req.user.email;

    const { userMessage, preferredDate, preferredTime } = req.body;

    if (!preferredDate || !preferredTime) {
      throw new ValidationError('User preferred date, and preferred time are required fields');
    }

    const newCoachCallRequest = new CoachCallRequest({
      userName: fullName,
      userEmail: email,
      userMessage,
      preferredDate,
      preferredTime,
    });

    await newCoachCallRequest.save();

    const formattedRequest = {
      id: newCoachCallRequest._id,
      userName: newCoachCallRequest.userName,
      userEmail: newCoachCallRequest.userEmail,
      userMessage: newCoachCallRequest.userMessage,
      preferredDate: newCoachCallRequest.preferredDate,
      preferredTime: newCoachCallRequest.preferredTime,
      status: newCoachCallRequest.status,
    };

    sendBookCoachCallEmail(req.user, formattedRequest);

    return formattedRequest;
  }
}

export default CoachCallRequestService;
