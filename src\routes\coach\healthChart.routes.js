import express from 'express';
import validateAccessToken from '../../middlewares/validateAccessToken.js';
import authenticateJWT from '../../middlewares/authenticateJWT.js';
import authorizeRoles from '../../middlewares/authorizeRoles.js';
import { UserRoles } from '../../constants/userRoles.js';
import {
  createHealthChart,
  publishHealthChart,
  getCoachHealthCharts,
  createFreeAdviceChart,
} from '../../controllers/healthChart.controller.js';

const router = express.Router();

// Apply global middlewares: JWT + Role-Based Authorization for Coaches
router.use(validateAccessToken, authenticateJWT, authorizeRoles(UserRoles.COACH));

/**
 * @route   GET /api/coaches/health-charts
 * @desc    Get health charts created by the authenticated coach
 * @access  Coach only
 * @query   { page?, limit?, status? }
 */
router.get('/', getCoachHealthCharts);

/**
 * @route   POST /api/coaches/health-charts
 * @desc    Create a new health chart
 * @access  Coach only
 */
router.post('/', createHealth<PERSON>hart);

/**
 * @route   POST /api/coaches/health-charts/free-advice
 * @desc    Create a free advice chart for a client
 * @access  Coach only
 */
router.post('/free-advice', createFreeAdviceChart);

/**
 * @route   PATCH /api/coaches/health-charts/:chartId/publish
 * @desc    Publish a health chart
 * @access  Coach only
 */
router.patch('/:chartId/publish', publishHealthChart);

export default router;
