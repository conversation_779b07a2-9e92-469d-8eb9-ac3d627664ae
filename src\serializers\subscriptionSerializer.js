/**
 * Subscription Serializer
 * Formats subscription data for API responses
 */
export const subscriptionSerializer = (subscriptionDoc) => {
  const {
    _id,
    userId,
    planId,
    durationId,
    orderId,
    status,
    startDate,
    endDate,
    isActive,
    features,
    createdAt,
    updatedAt,
  } = subscriptionDoc;

  return {
    id: _id,
    userId: userId,
    planId: planId,
    durationId: durationId,
    orderId: orderId,
    status: status,
    startDate: startDate,
    endDate: endDate,
    isActive: isActive,
    isExpired: subscriptionDoc.isExpired,
    features: features,
    createdAt: createdAt,
    updatedAt: updatedAt,
  };
};

/**
 * Subscription with Plan Details Serializer
 * Formats subscription data with populated plan information
 */
export const subscriptionWithPlanSerializer = (subscriptionDoc) => {
  const {
    _id,
    userId,
    planId,
    durationId,
    orderId,
    status,
    startDate,
    endDate,
    isActive,
    features,
    createdAt,
    updatedAt,
  } = subscriptionDoc;

  return {
    id: _id,
    userId: userId,
    plan: planId
      ? {
          id: planId._id,
          name: planId.name,
          slug: planId.slug,
          description: planId.description,
          isPopular: planId.isPopular,
        }
      : null,
    duration: durationId
      ? {
          id: durationId._id,
          label: durationId.label,
          valueInDays: durationId.valueInDays,
          price: durationId.price,
          currency: durationId.currency,
          paymentType: durationId.paymentType,
        }
      : null,
    order: orderId
      ? {
          id: orderId._id,
          razorpayOrderId: orderId.razorpay_order_id,
          razorpayPaymentId: orderId.razorpay_payment_id,
          amount: orderId.amount,
          currency: orderId.currency,
          status: orderId.status,
          paymentDate: orderId.paymentDate,
        }
      : null,
    status: status,
    startDate: startDate,
    endDate: endDate,
    isActive: isActive,
    isExpired: subscriptionDoc.isExpired,
    features: features
      ? features.map((feature) => ({
          id: feature._id,
          name: feature.name,
          description: feature.description,
          slug: feature.slug,
        }))
      : [],
    createdAt: createdAt,
    updatedAt: updatedAt,
  };
};

/**
 * User Active Subscription Serializer
 * Formats user's current active subscription
 */
export const activeSubscriptionSerializer = (subscriptionDoc) => {
  if (!subscriptionDoc) {
    return {
      hasActiveSubscription: false,
      subscription: null,
    };
  }

  const { _id, planId, durationId, status, startDate, endDate, isActive, features } = subscriptionDoc;

  return {
    hasActiveSubscription: isActive,
    subscription: {
      id: _id,
      plan: planId
        ? {
            id: planId._id,
            name: planId.name,
            slug: planId.slug,
          }
        : null,
      duration: durationId
        ? {
            id: durationId._id,
            label: durationId.label,
            valueInDays: durationId.valueInDays,
          }
        : null,
      status: status,
      startDate: startDate,
      endDate: endDate,
      isActive: isActive,
      isExpired: subscriptionDoc.isExpired,
      daysRemaining: endDate ? Math.max(0, Math.ceil((new Date(endDate) - new Date()) / (1000 * 60 * 60 * 24))) : 0,
      features: features
        ? features.map((feature) => ({
            id: feature._id,
            name: feature.name,
            slug: feature.slug,
          }))
        : [],
    },
  };
};

/**
 * Subscription History Serializer
 * Formats subscription history for listing
 */
export const subscriptionHistorySerializer = (subscriptions) => {
  return subscriptions.map((subscription) => ({
    id: subscription._id,
    planName: subscription.planId?.name || 'Unknown Plan',
    durationLabel: subscription.durationId?.label || 'Unknown Duration',
    status: subscription.status,
    startDate: subscription.startDate,
    endDate: subscription.endDate,
    isActive: subscription.isActive,
    isExpired: subscription.isExpired,
    createdAt: subscription.createdAt,
  }));
};

/**
 * Admin Subscription Overview Serializer
 * Formats subscription data for admin dashboard
 */
export const adminSubscriptionSerializer = (subscriptionDoc) => {
  const { _id, userId, planId, durationId, orderId, status, startDate, endDate, isActive, createdAt, updatedAt } =
    subscriptionDoc;

  return {
    id: _id,
    user: userId
      ? {
          id: userId._id,
          email: userId.email,
          firstName: userId.firstName,
          lastName: userId.lastName,
        }
      : null,
    plan: planId
      ? {
          id: planId._id,
          name: planId.name,
          slug: planId.slug,
        }
      : null,
    duration: durationId
      ? {
          id: durationId._id,
          label: durationId.label,
          valueInDays: durationId.valueInDays,
          price: durationId.price,
        }
      : null,
    order: orderId
      ? {
          id: orderId._id,
          amount: orderId.amount,
          status: orderId.status,
          paymentDate: orderId.paymentDate,
        }
      : null,
    status: status,
    startDate: startDate,
    endDate: endDate,
    isActive: isActive,
    isExpired: subscriptionDoc.isExpired,
    createdAt: createdAt,
    updatedAt: updatedAt,
  };
};
