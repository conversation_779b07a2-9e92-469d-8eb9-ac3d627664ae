import express from 'express';

// Import admin route modules
import clientsRoutes from './clients.routes.js';
import dashboardRoutes from './dashboard.routes.js';
import paymentsRoutes from './payments.routes.js';
import subscriptionsRoutes from './subscriptions.routes.js';
import couponsRoutes from './coupons.routes.js';
import pricingRoutes from './pricing/index.js';

const router = express.Router();

/**
 * ===== ADMIN API ROUTES =====
 * All routes require admin authentication and authorization
 */

/**
 * @route   GET /api/admin/dashboard
 * @desc    Get comprehensive dashboard overview with analytics
 * @access  Admin only
 */
router.use('/dashboard', dashboardRoutes);

/**
 * @route   GET /api/admin/clients
 * @desc    Get paginated list of all clients
 * @access  Admin only
 */
router.use('/clients', clientsRoutes);

/**
 * @route   GET /api/admin/payments
 * @desc    Get all payments with filters
 * @access  Admin only
 */
router.use('/payments', paymentsRoutes);

/**
 * @route   GET /api/admin/subscriptions
 * @desc    Get all subscriptions with filters
 * @access  Admin only
 */
router.use('/subscriptions', subscriptionsRoutes);

/**
 * @route   GET /api/admin/coupons
 * @desc    Get all coupons with filters
 * @access  Admin only
 */
router.use('/coupons', couponsRoutes);

/**
 * @route   GET /api/admin/pricing
 * @desc    Get all pricing plans and options
 * @access  Admin only
 */
router.use('/pricing', pricingRoutes);

export default router;
