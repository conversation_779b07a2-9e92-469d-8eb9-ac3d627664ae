/**
 * Test setup and configuration
 * This file is run before all tests to set up the testing environment
 */

import { jest } from '@jest/globals';
import mongoose from 'mongoose';
import dotenv from 'dotenv';

// Load test environment variables
dotenv.config({ path: '.env.test' });

// Set test environment
process.env.NODE_ENV = 'test';

// Mock console methods to reduce noise during tests
const originalConsoleLog = console.log;
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

// Only show console output if VERBOSE_TESTS is set
if (!process.env.VERBOSE_TESTS) {
  console.log = jest.fn();
  console.warn = jest.fn();
  // Keep error logs for debugging
  console.error = (...args) => {
    if (args[0] && args[0].includes && args[0].includes('Test')) {
      originalConsoleError(...args);
    }
  };
}

// Global test setup
beforeAll(async () => {
  // Set up test database connection
  const testDbUri = process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/ffs_test';
  
  if (mongoose.connection.readyState === 0) {
    await mongoose.connect(testDbUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
  }
});

// Global test cleanup
afterAll(async () => {
  // Clean up database and close connection
  if (mongoose.connection.readyState !== 0) {
    await mongoose.connection.dropDatabase();
    await mongoose.connection.close();
  }

  // Restore console methods
  if (!process.env.VERBOSE_TESTS) {
    console.log = originalConsoleLog;
    console.error = originalConsoleError;
    console.warn = originalConsoleWarn;
  }
});

// Global test configuration
jest.setTimeout(30000); // 30 second timeout for tests

// Mock external services
jest.mock('../src/mailer/bookCoachCall.js', () => ({
  __esModule: true,
  default: jest.fn().mockResolvedValue(true),
}));

// Mock payment services
jest.mock('../src/services/razorpay.service.js', () => ({
  __esModule: true,
  default: {
    createOrder: jest.fn().mockResolvedValue({ id: 'mock_order_id' }),
    verifyPayment: jest.fn().mockResolvedValue(true),
  },
}));

// Mock file upload services
jest.mock('../src/utils/fileUpload.js', () => ({
  __esModule: true,
  uploadFile: jest.fn().mockResolvedValue({ url: 'mock_file_url' }),
  deleteFile: jest.fn().mockResolvedValue(true),
}));

// Mock notification services
jest.mock('../src/services/notification.service.js', () => ({
  __esModule: true,
  default: {
    sendRealTimeNotification: jest.fn().mockResolvedValue(true),
    createUsageLimitNotification: jest.fn().mockResolvedValue({ id: 'mock_notification' }),
  },
}));

// Helper functions for tests
global.createMockUser = (overrides = {}) => ({
  _id: new mongoose.Types.ObjectId(),
  firstName: 'Test',
  lastName: 'User',
  email: '<EMAIL>',
  roles: 'Client',
  isVerified: true,
  isOnboarded: false,
  ...overrides,
});

global.createMockSubscription = (userId, overrides = {}) => ({
  _id: new mongoose.Types.ObjectId(),
  userId,
  planId: new mongoose.Types.ObjectId(),
  durationId: new mongoose.Types.ObjectId(),
  isActive: true,
  status: 'active',
  startDate: new Date(),
  endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
  ...overrides,
});

global.createMockHealthChart = (coachId, clientId, clientUserId, overrides = {}) => ({
  _id: new mongoose.Types.ObjectId(),
  title: 'Test Health Chart',
  chartType: 'advice_chart',
  coachId,
  clientId,
  clientUserId,
  content: { textContent: 'Test content' },
  isActive: true,
  isPublished: true,
  publishedAt: new Date(),
  ...overrides,
});

// Test utilities
global.testUtils = {
  /**
   * Wait for a specified amount of time
   * @param {number} ms - Milliseconds to wait
   */
  wait: (ms) => new Promise(resolve => setTimeout(resolve, ms)),

  /**
   * Generate a random email for testing
   */
  randomEmail: () => `test${Date.now()}${Math.random().toString(36).substr(2, 5)}@example.com`,

  /**
   * Generate a random ObjectId
   */
  randomObjectId: () => new mongoose.Types.ObjectId(),

  /**
   * Clean up test data from collections
   * @param {Array} collections - Array of collection names to clean
   */
  cleanupCollections: async (collections) => {
    for (const collectionName of collections) {
      if (mongoose.connection.collections[collectionName]) {
        await mongoose.connection.collections[collectionName].deleteMany({});
      }
    }
  },
};

export default {
  testEnvironment: 'node',
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  testMatch: [
    '<rootDir>/tests/**/*.test.js',
    '<rootDir>/tests/**/*.spec.js',
  ],
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/app.js',
    '!src/server.js',
    '!src/config/**',
    '!src/constants/**',
    '!**/node_modules/**',
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  verbose: true,
};
