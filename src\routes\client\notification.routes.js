import express from 'express';
import validateAccessToken from '../../middlewares/validateAccessToken.js';
import authenticateJWT from '../../middlewares/authenticateJWT.js';
import authorizeRoles from '../../middlewares/authorizeRoles.js';
import { UserRoles } from '../../constants/userRoles.js';
import {
  getUserNotifications,
  getNotificationSummary,
  getFeatureNotifications,
  getUpgradePrompts,
  markNotificationAsRead,
} from '../../controllers/notification.controller.js';

const router = express.Router();

// Apply global middlewares: JWT + Role-Based Authorization for Clients
router.use(validateAccessToken, authenticateJWT, authorizeRoles(UserRoles.CLIENT));

/**
 * @route   GET /api/clients/notifications/summary
 * @desc    Get notification summary for the user
 * @access  Client only
 */
router.get('/summary', getNotificationSummary);

/**
 * @route   GET /api/clients/notifications/upgrade-prompts
 * @desc    Get upgrade prompts for the user
 * @access  Client only
 */
router.get('/upgrade-prompts', getUpgradePrompts);

/**
 * @route   GET /api/clients/notifications/feature/:featureType
 * @desc    Get feature-specific notifications
 * @access  Client only
 */
router.get('/feature/:featureType', getFeatureNotifications);

/**
 * @route   GET /api/clients/notifications
 * @desc    Get all notifications for the user
 * @access  Client only
 */
router.get('/', getUserNotifications);

/**
 * @route   PATCH /api/clients/notifications/:notificationId/read
 * @desc    Mark a notification as read
 * @access  Client only
 */
router.patch('/:notificationId/read', markNotificationAsRead);

export default router;
