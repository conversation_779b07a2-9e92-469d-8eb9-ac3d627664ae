import { FreeFeatureType, FeatureUsageStatus } from '../constants/enums.js';
import { FeatureAccessMessages, FreeFeatureLimits } from '../constants/featureLimits.js';
import { ValidationError, ForbiddenError } from '../utils/errorHandler.js';
import { HttpStatus } from '../constants/httpStatus.js';
import UserFeatureUsageModel from '../models/userFeatureUsage.model.js';
import subscriptionModel from '../models/order/subscription.model.js';

/**
 * Middleware to check if user has an active subscription
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const checkActiveSubscription = async (req, res, next) => {
  try {
    const userId = req.user.id || req.user._id;

    // Check for active subscription
    const activeSubscription = await subscriptionModel.findOne({
      userId,
      isActive: true,
      status: 'active',
      endDate: { $gt: new Date() },
    });

    // Add subscription info to request object
    req.userSubscription = {
      hasActiveSubscription: !!activeSubscription,
      subscription: activeSubscription,
    };

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Middleware to check feature access based on subscription and usage limits
 * @param {string} featureType - The feature type to check
 * @param {Object} options - Additional options for feature checking
 * @returns {Function} Express middleware function
 */
export const checkFeatureAccess = (featureType, options = {}) => {
  return async (req, res, next) => {
    try {
      const userId = req.user.id || req.user._id;
      const { requireSubscription = false, allowFreeUsage = true } = options;

      // Validate feature type
      if (!FreeFeatureType.ALL.includes(featureType)) {
        throw new ValidationError(`Invalid feature type: ${featureType}`);
      }

      // Check if user has active subscription
      let hasActiveSubscription = false;
      if (req.userSubscription) {
        hasActiveSubscription = req.userSubscription.hasActiveSubscription;
      } else {
        // Check subscription if not already checked
        const activeSubscription = await subscriptionModel.findOne({
          userId,
          isActive: true,
          status: 'active',
          endDate: { $gt: new Date() },
        });
        hasActiveSubscription = !!activeSubscription;
        req.userSubscription = {
          hasActiveSubscription,
          subscription: activeSubscription,
        };
      }

      // If user has active subscription, allow access
      if (hasActiveSubscription) {
        req.featureAccess = {
          hasAccess: true,
          accessType: 'subscription',
          unlimited: true,
        };
        return next();
      }

      // If subscription is required and user doesn't have one
      if (requireSubscription && !hasActiveSubscription) {
        throw new ForbiddenError(FeatureAccessMessages.SUBSCRIPTION_REQUIRED);
      }

      // Check free usage limits if free usage is allowed
      if (allowFreeUsage) {
        const usageInfo = await UserFeatureUsageModel.checkFeatureAccess(userId, featureType);

        if (!usageInfo.canUse) {
          const limit = FreeFeatureLimits.getLimit(featureType);
          const message = FeatureAccessMessages.getFeatureLimitMessage(featureType, limit);
          throw new ForbiddenError(message);
        }

        // Add feature access info to request
        req.featureAccess = {
          hasAccess: true,
          accessType: 'free',
          unlimited: false,
          usageInfo,
        };

        return next();
      }

      // If no subscription and free usage not allowed
      throw new ForbiddenError(FeatureAccessMessages.SUBSCRIPTION_REQUIRED);
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Middleware specifically for coach call access
 */
export const checkCoachCallAccess = checkFeatureAccess(FreeFeatureType.FREE_COACH_CALL, {
  allowFreeUsage: true,
  requireSubscription: false,
});

/**
 * Middleware specifically for advice chart access
 */
export const checkAdviceChartAccess = checkFeatureAccess(FreeFeatureType.FREE_ADVICE_CHART, {
  allowFreeUsage: true,
  requireSubscription: false,
});

/**
 * Middleware to record feature usage after successful feature use
 * @param {string} featureType - The feature type to record
 * @param {Function} getEntityInfo - Function to extract entity info from request
 * @returns {Function} Express middleware function
 */
export const recordFeatureUsage = (featureType, getEntityInfo = null) => {
  return async (req, res, next) => {
    try {
      const userId = req.user.id || req.user._id;

      // Skip recording if user has unlimited access (subscription)
      if (req.featureAccess && req.featureAccess.unlimited) {
        return next();
      }

      // Extract entity information if function provided
      let entityInfo = {};
      if (getEntityInfo && typeof getEntityInfo === 'function') {
        entityInfo = getEntityInfo(req, res);
      }

      // Record the usage
      const updatedUsage = await UserFeatureUsageModel.recordUsage(userId, featureType, entityInfo);

      // Add updated usage info to request for potential use in response
      req.updatedFeatureUsage = updatedUsage;

      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Middleware to get user's feature usage summary
 */
export const getUserFeatureUsage = async (req, res, next) => {
  try {
    const userId = req.user.id || req.user._id;

    // Get all feature usage for the user
    const featureUsage = await UserFeatureUsageModel.getUserFeatureUsage(userId);

    // Check subscription status
    const hasActiveSubscription = req.userSubscription?.hasActiveSubscription || false;

    // Format feature usage summary
    const usageSummary = {
      hasActiveSubscription,
      features: {},
    };

    // Add usage info for each feature type
    for (const featureType of FreeFeatureType.ALL) {
      const usage = featureUsage.find((u) => u.featureType === featureType);
      const limit = FreeFeatureLimits.getLimit(featureType);

      if (usage) {
        usageSummary.features[featureType] = {
          usageCount: usage.usageCount,
          usageLimit: usage.usageLimit,
          remainingUsage: usage.getRemainingUsage(),
          status: usage.status,
          isExhausted: usage.isExhausted,
          canUse: hasActiveSubscription || usage.canUseFeature(),
          lastUsedAt: usage.lastUsedAt,
        };
      } else {
        usageSummary.features[featureType] = {
          usageCount: 0,
          usageLimit: limit,
          remainingUsage: limit,
          status: FeatureUsageStatus.AVAILABLE,
          isExhausted: false,
          canUse: hasActiveSubscription || limit > 0,
          lastUsedAt: null,
        };
      }
    }

    req.featureUsageSummary = usageSummary;
    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Middleware to validate feature access for admin operations
 * @param {string} requiredRole - Required role for access
 * @returns {Function} Express middleware function
 */
export const validateAdminFeatureAccess = (requiredRole = 'Admin') => {
  return (req, res, next) => {
    try {
      const userRole = req.user.roles;

      if (userRole !== requiredRole) {
        throw new ForbiddenError('Insufficient permissions for this operation');
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Utility function to check if user can access unlimited features
 * @param {string} userId - User ID
 * @returns {Promise<boolean>} True if user has unlimited access
 */
export const hasUnlimitedAccess = async (userId) => {
  const activeSubscription = await subscriptionModel.findOne({
    userId,
    isActive: true,
    status: 'active',
    endDate: { $gt: new Date() },
  });

  return !!activeSubscription;
};

/**
 * Utility function to get feature access status for a user
 * @param {string} userId - User ID
 * @param {string} featureType - Feature type
 * @returns {Promise<Object>} Feature access information
 */
export const getFeatureAccessStatus = async (userId, featureType) => {
  const hasSubscription = await hasUnlimitedAccess(userId);

  if (hasSubscription) {
    return {
      hasAccess: true,
      accessType: 'subscription',
      unlimited: true,
      usageInfo: null,
    };
  }

  const usageInfo = await UserFeatureUsageModel.checkFeatureAccess(userId, featureType);

  return {
    hasAccess: usageInfo.canUse,
    accessType: 'free',
    unlimited: false,
    usageInfo,
  };
};

/**
 * Express error handler for feature access errors
 */
export const handleFeatureAccessError = (error, req, res, next) => {
  if (error instanceof ForbiddenError) {
    return res.status(HttpStatus.STATUS_CODE.FORBIDDEN).json({
      success: false,
      message: error.message,
      errorCode: 'FEATURE_ACCESS_DENIED',
      featureAccess: req.featureAccess || null,
      featureUsage: req.featureUsageSummary || null,
    });
  }

  if (error instanceof ValidationError) {
    return res.status(HttpStatus.STATUS_CODE.BAD_REQUEST).json({
      success: false,
      message: error.message,
      errorCode: 'INVALID_FEATURE_REQUEST',
    });
  }

  next(error);
};
