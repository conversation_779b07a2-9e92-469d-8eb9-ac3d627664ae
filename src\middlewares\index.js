import cors from 'cors';
import helmet from 'helmet';
import express from 'express';
import cookieParser from 'cookie-parser';

import config from '../config/environment.config.js';
import { Constants } from '../constants/general.js';
import rateLimiter from './rateLimiter.js';
import httpLogger from './httpLogger.js';
import { sanitizeRequest } from './sanitizeRequest.js';

const setupMiddlewares = (app) => {
  // Parse allowed origins from comma-separated env variable
  const allowedOrigins = (config.allowed_origins || '')
    .split(',')
    .map((origin) => origin.trim())
    .filter(Boolean);

  app.use(
    cors({
      origin: function (origin, callback) {
        if (!origin) return callback(null, true);
        if (allowedOrigins.includes(origin)) {
          return callback(null, true);
        } else {
          return callback(new Error('Not allowed by CORS'));
        }
      },
      methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
      credentials: true,
      exposedHeaders: [Constants.HEADERS.AUTHORIZATION],
      maxAge: 3600,
    })
  );

  app.use(httpLogger);
  app.use(rateLimiter(500, 5 * 60 * 1000));
  app.use(express.json({ limit: '16kb' }));
  app.use(express.urlencoded({ extended: true, limit: '16kb' }));
  app.use(cookieParser());
  app.use(sanitizeRequest);
  app.use(helmet());
};

export default setupMiddlewares;
