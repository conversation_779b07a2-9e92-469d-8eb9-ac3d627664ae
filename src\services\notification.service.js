import { FreeFeatureType } from '../constants/enums.js';
import { FeatureAccessMessages, FreeFeatureLimits } from '../constants/featureLimits.js';
import UserFeatureUsageModel from '../models/userFeatureUsage.model.js';
import SubscriptionUtils from '../utils/subscriptionUtils.js';
import userModel from '../models/user.model.js';

/**
 * Notification Service
 * Handles usage limit notifications and subscription upgrade prompts
 */
class NotificationService {
  /**
   * Get all notifications for a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} User notifications
   */
  static async getUserNotifications(userId) {
    const subscriptionInfo = await SubscriptionUtils.checkActiveSubscription(userId);
    const featureUsage = await UserFeatureUsageModel.getUserFeatureUsage(userId);

    const notifications = {
      urgent: [],
      warning: [],
      info: [],
      promotional: [],
    };

    // Skip notifications for users with active subscriptions
    if (subscriptionInfo.hasActiveSubscription) {
      // Add subscription-related notifications if needed
      const expirationInfo = await SubscriptionUtils.checkSubscriptionExpiration(userId);
      
      if (expirationInfo.shouldWarn) {
        notifications.warning.push({
          id: 'subscription_expiring',
          type: 'subscription_expiration',
          title: 'Subscription Expiring Soon',
          message: `Your subscription expires in ${expirationInfo.daysRemaining} days`,
          actionText: 'Renew Subscription',
          actionUrl: '/subscription/renew',
          priority: 'high',
          createdAt: new Date(),
        });
      }

      return notifications;
    }

    // Feature usage notifications for free users
    for (const featureType of FreeFeatureType.ALL) {
      const usage = featureUsage.find(u => u.featureType === featureType);
      const limit = FreeFeatureLimits.getLimit(featureType);

      if (usage) {
        const remainingUsage = usage.getRemainingUsage();
        const usagePercentage = (usage.usageCount / usage.usageLimit) * 100;

        // Exhausted feature notification
        if (usage.isExhausted) {
          notifications.urgent.push({
            id: `${featureType}_exhausted`,
            type: 'feature_exhausted',
            featureType,
            title: `${this.getFeatureDisplayName(featureType)} Limit Reached`,
            message: FeatureAccessMessages.getFeatureLimitMessage(featureType, limit),
            actionText: 'Upgrade to Premium',
            actionUrl: '/subscription/plans',
            priority: 'high',
            createdAt: new Date(),
          });
        }
        // Warning for high usage
        else if (usagePercentage >= 80) {
          notifications.warning.push({
            id: `${featureType}_warning`,
            type: 'feature_warning',
            featureType,
            title: `${this.getFeatureDisplayName(featureType)} Almost Used Up`,
            message: FeatureAccessMessages.getUsageWarningMessage(featureType, remainingUsage),
            actionText: 'View Usage',
            actionUrl: `/feature-usage/${featureType}`,
            priority: 'medium',
            createdAt: new Date(),
          });
        }
      }
    }

    // Upgrade promotional notifications
    const upgradeRecommendations = await SubscriptionUtils.getUpgradeRecommendations(userId);
    
    if (upgradeRecommendations.shouldUpgrade) {
      notifications.promotional.push({
        id: 'upgrade_recommendation',
        type: 'upgrade_promotion',
        title: 'Unlock Unlimited Access',
        message: 'Upgrade to premium for unlimited coach calls and health charts',
        actionText: 'View Plans',
        actionUrl: '/subscription/plans',
        priority: 'medium',
        benefits: upgradeRecommendations.benefits,
        createdAt: new Date(),
      });
    }

    // Welcome notification for new users
    const user = await userModel.findById(userId);
    if (user && !user.isOnboarded) {
      notifications.info.push({
        id: 'welcome_message',
        type: 'welcome',
        title: 'Welcome to Food For Soul!',
        message: 'You have 1 free coach call and 1 free health chart available',
        actionText: 'Get Started',
        actionUrl: '/onboarding',
        priority: 'low',
        createdAt: new Date(),
      });
    }

    return notifications;
  }

  /**
   * Get feature-specific notifications
   * @param {string} userId - User ID
   * @param {string} featureType - Feature type
   * @returns {Promise<Array>} Feature notifications
   */
  static async getFeatureNotifications(userId, featureType) {
    const subscriptionInfo = await SubscriptionUtils.checkActiveSubscription(userId);
    
    if (subscriptionInfo.hasActiveSubscription) {
      return [];
    }

    const usageInfo = await UserFeatureUsageModel.checkFeatureAccess(userId, featureType);
    const notifications = [];

    if (!usageInfo.canUse) {
      notifications.push({
        id: `${featureType}_blocked`,
        type: 'feature_blocked',
        featureType,
        title: 'Feature Limit Reached',
        message: FeatureAccessMessages.getFeatureLimitMessage(featureType, usageInfo.usageLimit),
        actionText: 'Upgrade Now',
        actionUrl: '/subscription/plans',
        priority: 'high',
        createdAt: new Date(),
      });
    } else if (usageInfo.remainingUsage === 1) {
      notifications.push({
        id: `${featureType}_last_use`,
        type: 'feature_last_use',
        featureType,
        title: 'Last Free Use Available',
        message: `This is your last free ${this.getFeatureDisplayName(featureType).toLowerCase()}`,
        actionText: 'Use Carefully',
        actionUrl: null,
        priority: 'medium',
        createdAt: new Date(),
      });
    }

    return notifications;
  }

  /**
   * Create usage limit notification
   * @param {string} userId - User ID
   * @param {string} featureType - Feature type
   * @param {string} notificationType - Type of notification
   * @returns {Promise<Object>} Created notification
   */
  static async createUsageLimitNotification(userId, featureType, notificationType) {
    const usageInfo = await UserFeatureUsageModel.checkFeatureAccess(userId, featureType);
    const featureDisplayName = this.getFeatureDisplayName(featureType);

    let notification = {
      id: `${featureType}_${notificationType}_${Date.now()}`,
      type: notificationType,
      featureType,
      userId,
      createdAt: new Date(),
    };

    switch (notificationType) {
      case 'limit_reached':
        notification = {
          ...notification,
          title: `${featureDisplayName} Limit Reached`,
          message: FeatureAccessMessages.getFeatureLimitMessage(featureType, usageInfo.usageLimit),
          priority: 'high',
          actionText: 'Upgrade to Premium',
          actionUrl: '/subscription/plans',
        };
        break;

      case 'usage_warning':
        notification = {
          ...notification,
          title: `${featureDisplayName} Almost Used Up`,
          message: FeatureAccessMessages.getUsageWarningMessage(featureType, usageInfo.remainingUsage),
          priority: 'medium',
          actionText: 'View Usage Details',
          actionUrl: `/feature-usage/${featureType}`,
        };
        break;

      case 'first_use':
        notification = {
          ...notification,
          title: `${featureDisplayName} Used`,
          message: `You have ${usageInfo.remainingUsage} free uses remaining`,
          priority: 'low',
          actionText: 'Learn More',
          actionUrl: `/features/${featureType}`,
        };
        break;

      default:
        throw new Error(`Invalid notification type: ${notificationType}`);
    }

    // In a real implementation, you might save this to a notifications collection
    return notification;
  }

  /**
   * Get subscription upgrade prompts
   * @param {string} userId - User ID
   * @returns {Promise<Array>} Upgrade prompts
   */
  static async getUpgradePrompts(userId) {
    const subscriptionInfo = await SubscriptionUtils.checkActiveSubscription(userId);
    
    if (subscriptionInfo.hasActiveSubscription) {
      return [];
    }

    const upgradeRecommendations = await SubscriptionUtils.getUpgradeRecommendations(userId);
    const prompts = [];

    if (upgradeRecommendations.shouldUpgrade) {
      prompts.push({
        id: 'primary_upgrade_prompt',
        type: 'upgrade_prompt',
        title: 'Unlock Unlimited Access',
        message: 'Get unlimited coach calls, health charts, and premium features',
        benefits: upgradeRecommendations.benefits,
        actionText: 'View Plans',
        actionUrl: '/subscription/plans',
        priority: 'high',
        style: 'primary',
      });

      // Add feature-specific prompts
      for (const recommendation of upgradeRecommendations.recommendations) {
        prompts.push({
          id: `${recommendation.featureType}_upgrade_prompt`,
          type: 'feature_upgrade_prompt',
          featureType: recommendation.featureType,
          title: `Unlock ${this.getFeatureDisplayName(recommendation.featureType)}`,
          message: `You've reached your free limit. Upgrade for unlimited access.`,
          actionText: 'Upgrade Now',
          actionUrl: '/subscription/plans',
          priority: recommendation.priority,
          style: 'secondary',
        });
      }
    }

    return prompts;
  }

  /**
   * Get notification summary for a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Notification summary
   */
  static async getNotificationSummary(userId) {
    const notifications = await this.getUserNotifications(userId);
    const upgradePrompts = await this.getUpgradePrompts(userId);

    const summary = {
      totalNotifications: 0,
      urgentCount: notifications.urgent.length,
      warningCount: notifications.warning.length,
      infoCount: notifications.info.length,
      promotionalCount: notifications.promotional.length,
      upgradePromptsCount: upgradePrompts.length,
      hasUrgent: notifications.urgent.length > 0,
      hasWarnings: notifications.warning.length > 0,
      shouldShowUpgrade: upgradePrompts.length > 0,
    };

    summary.totalNotifications = summary.urgentCount + summary.warningCount + 
                                summary.infoCount + summary.promotionalCount;

    return summary;
  }

  /**
   * Mark notification as read
   * @param {string} userId - User ID
   * @param {string} notificationId - Notification ID
   * @returns {Promise<Object>} Update result
   */
  static async markNotificationAsRead(userId, notificationId) {
    // In a real implementation, this would update the notification in the database
    return {
      notificationId,
      userId,
      markedAsRead: true,
      readAt: new Date(),
    };
  }

  /**
   * Get feature display name
   * @param {string} featureType - Feature type
   * @returns {string} Display name
   */
  static getFeatureDisplayName(featureType) {
    const displayNames = {
      [FreeFeatureType.FREE_COACH_CALL]: 'Coach Call',
      [FreeFeatureType.FREE_ADVICE_CHART]: 'Health Chart',
    };
    return displayNames[featureType] || featureType;
  }

  /**
   * Send real-time notification (placeholder for WebSocket/push notifications)
   * @param {string} userId - User ID
   * @param {Object} notification - Notification data
   * @returns {Promise<void>}
   */
  static async sendRealTimeNotification(userId, notification) {
    // This would integrate with WebSocket or push notification service
    console.log(`Real-time notification for user ${userId}:`, notification);
  }

  /**
   * Schedule notification cleanup (remove old notifications)
   * @param {number} daysOld - Days old to consider for cleanup
   * @returns {Promise<Object>} Cleanup result
   */
  static async cleanupOldNotifications(daysOld = 30) {
    // This would clean up old notifications from the database
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    return {
      cleanupDate: cutoffDate,
      message: `Notifications older than ${daysOld} days would be cleaned up`,
    };
  }
}

export default NotificationService;
