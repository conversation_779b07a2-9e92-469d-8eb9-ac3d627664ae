# ffs-backend-node

Backend for Food For Soul (FFS) - Node.js

## Overview

This is the backend server for the Food For Soul platform, built with Node.js, Express, and MongoDB. It provides RESTful APIs for authentication, user management, client and admin operations, sleep and step tracking, order and payment processing, and more.

## Getting Started

### Prerequisites

- Node.js v18+
- npm v8+
- MongoDB instance

### Installation

1. Clone the repository:
   ```sh
   git clone <repo-url>
   cd ffs-backend-node
   ```
2. Install dependencies:
   ```sh
   npm install
   ```
3. Create a `.env` file in the root directory and configure the following variables:
   ```env
   PORT=8000
   NODE_ENV=development
   DATABASE_URL=mongodb://localhost:27017/ffs
   DATABASE_NAME=ffs
   JWT_ACCESS_TOKEN_SECRET_KEY=your_access_secret
   JWT_REFRESH_TOKEN_SECRET_KEY=your_refresh_secret
   JWT_ALGORITHM=HS256
   SALT_ROUNDS=10
   SMTP_HOST=smtp.example.com
   SMTP_PORT=587
   SMTP_USER=<EMAIL>
   SMTP_PASS=your_email_password
   EMAIL_FROM=<EMAIL>
   FRONTEND_HOST=http://localhost:3000
   RAZORPAY_KEY_ID=your_razorpay_key_id
   RAZORPAY_KEY_SECRET=your_razorpay_key_secret
   RAZORPAY_WEBHOOK_SECRET=your_razorpay_webhook_secret
   ```

### Running the Server

- For development (with auto-reload):
  ```sh
  npm run dev
  ```
- For production:
  ```sh
  npm start
  ```

---

## Main Features

- **Authentication:** JWT-based login, registration, password reset, email verification, and admin login.
- **User & Client Management:** CRUD operations for users and clients.
- **Order & Payment:** Coupon, payment, and subscription management with Razorpay integration.
- **Tracking:** Sleep and step tracking APIs.
- **Admin & Client APIs:** Separate routes for admin and client operations.
- **Email Services:** OTP, verification, and transactional emails.
- **Security:** Helmet, rate limiting, input sanitization, and CORS.
- **Logging:** Winston-based logging for errors and requests.
- **Health Checks:** Endpoints for server and system health monitoring.

---

## API Endpoints

- `/api/auth` - Authentication (signup, login, password, email verification, etc.)
- `/api/clients` - Client-specific APIs
- `/api/admin` - Admin-specific APIs
- `/api/health` - Health check endpoints
- `/api` - Public APIs

See the `src/routes/` and controller files for detailed route information.

---

## Scripts

- `npm start` - Start the server
- `npm run dev` - Start the server with nodemon (development)
- `npm run format` - Format code with Prettier

---

## Logging

- Logs are stored in the `logs/` directory (`app.log`, `error.log`).

---
