import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import config from '../config/environment.config.js';

// Models
import userModel from '../models/user.model.js';
import passwordResetModel from '../models/passwordReset.modal.js';
import userRefreshTokenModel from '../models/userRefreshToken.modal.js';
import emailVerificationModel from '../models/emailVerification.modal.js';

import { AppError, NotFoundError, ValidationError } from '../utils/errorHandler.js';
import sendResetPasswordEmail from '../mailer/sendResetPasswordEmail.js';
import sendEmailVerificationOtp from '../mailer/sendEmailVerificationOtp.js';
import generateOtp from '../mailer/generateOtp.js';
import { HttpStatus } from '../constants/httpStatus.js';
import { ValidationMessages } from '../constants/messages.js';
import generateTokens from '../utils/generateTokens.js';
import invalidateSession from '../utils/invalidateSession.js';
import { UserRoles } from '../constants/userRoles.js';
import userSerializer from '../serializers/userSerializer.js';
import { setupDefaultUserEnvironment } from '../utils/userSetup.js';

class AuthService {
  static async register(req, res) {
    if (!req.body) {
      throw new ValidationError(
        ValidationMessages.GENERAL.INVALID_REQUEST_BODY,
        HttpStatus.STATUS_CODE.UNPROCESSABLE_ENTITY
      );
    }

    const { first_name, last_name, email, password, password_confirmation } = req.body;

    if (!first_name || !last_name || !email || !password || !password_confirmation) {
      throw new ValidationError('First name, last name, email, password and password confirmation are required fields');
    }

    if (password !== password_confirmation) {
      throw new ValidationError('Password and password confirmation do not match');
    }
    // Check if email already exists
    const existingUser = await userModel.findOne({ email });
    if (existingUser) {
      throw new AppError('Email is already registered', HttpStatus.STATUS_CODE.CONFLICT);
    }

    // Hash the password
    const saltRounds = parseInt(config.salt) || 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Create the new user
    const newUser = new userModel({
      email,
      firstName: first_name,
      lastName: last_name,
      password: hashedPassword,
    });

    await newUser.save();

    const { clientProfile, userSettings, sleep, water, steps } = await setupDefaultUserEnvironment(newUser._id);

    newUser.profile = clientProfile._id;
    await newUser.save();

    // Send OTP email for email verification
    await sendEmailVerificationOtp(req, newUser);

    return userSerializer(newUser, { blacklist: ['_id', 'roles', 'profile'] });
  }

  static sendVerificationEmail = async (req, res) => {
    if (!req.body) {
      throw new ValidationError(
        ValidationMessages.GENERAL.INVALID_REQUEST_BODY,
        HttpStatus.STATUS_CODE.UNPROCESSABLE_ENTITY
      );
    }

    const { email } = req.body;

    if (!email) {
      throw new ValidationError(ValidationMessages.AUTHENTICATION.EMAIL_REQUIRED);
    }

    const existingUser = await userModel.findOne({ email });
    if (!existingUser) {
      throw new NotFoundError(ValidationMessages.AUTHENTICATION.ACCOUNT_NOT_FOUND);
    }

    // Check if email is already verified
    if (existingUser.isVerified) {
      throw new ValidationError('Email is already verified', HttpStatus.STATUS_CODE.BAD_REQUEST);
    }

    // Send OTP email for email verification
    await sendEmailVerificationOtp(req, existingUser);

    return null;
  };

  static verifyEmail = async (req, res) => {
    if (!req.body) {
      throw new ValidationError(
        ValidationMessages.GENERAL.INVALID_REQUEST_BODY,
        HttpStatus.STATUS_CODE.UNPROCESSABLE_ENTITY
      );
    }

    const { email, otp } = req.body;

    if (!email || !otp) {
      throw new ValidationError(ValidationMessages.GENERAL.MISSING_FIELDS, HttpStatus.STATUS_CODE.BAD_REQUEST);
    }

    const existingUser = await userModel.findOne({ email });
    if (!existingUser) {
      throw new NotFoundError(ValidationMessages.AUTHENTICATION.ACCOUNT_NOT_FOUND);
    }

    // Check if email is already verified
    if (existingUser.isVerified) {
      throw new ValidationError('Email is already verified', HttpStatus.STATUS_CODE.BAD_REQUEST);
    }

    // Check if there is a matching email verification OTP
    const emailVerification = await emailVerificationModel.findOne({ userId: existingUser._id, otp });
    if (!emailVerification) {
      throw new ValidationError(ValidationMessages.AUTHENTICATION.INVALID_OTP, HttpStatus.STATUS_CODE.BAD_REQUEST);
    }

    // Check if OTP is expired
    const currentTime = new Date();
    // 15 * 60 * 1000 calculates the expiration period in milliseconds(15 minutes).
    const expirationTime = new Date(emailVerification.createdAt.getTime() + 15 * 60 * 1000);
    if (currentTime > expirationTime) {
      throw new ValidationError(ValidationMessages.AUTHENTICATION.OTP_EXPIRED, HttpStatus.STATUS_CODE.BAD_REQUEST);
    }

    // OTP is valid and not expired, mark email as verified
    existingUser.isVerified = true;
    await existingUser.save();

    // Delete email verification document
    await emailVerificationModel.deleteMany({ userId: existingUser._id });

    return null;
  };

  static async login(req, res) {
    if (!req.body) {
      throw new ValidationError(
        ValidationMessages.GENERAL.INVALID_REQUEST_BODY,
        HttpStatus.STATUS_CODE.UNPROCESSABLE_ENTITY
      );
    }

    const { email, password } = req.body;

    if (!email || !password) {
      throw new ValidationError('Email and password are required fields');
    }

    // Check if the user exists
    const user = await userModel.findOne({ email });
    if (!user) {
      throw new NotFoundError(ValidationMessages.AUTHENTICATION.ACCOUNT_NOT_FOUND);
    }

    // Check if the user is deactivated
    // if (!user.isVerified) {
    //   throw new AppError(ValidationMessages.AUTHENTICATION.ACCOUNT_NOT_VERIFIED, HttpStatus.STATUS_CODE.UNAUTHORIZED);
    // }

    // Check if the user is deactivated
    if (user.isDeactivated) {
      throw new AppError(ValidationMessages.AUTHENTICATION.DEACTIVATED, HttpStatus.STATUS_CODE.FORBIDDEN);
    }

    // Check if the password is correct
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new AppError(ValidationMessages.AUTHENTICATION.INVALID_CREDENTIALS, HttpStatus.STATUS_CODE.UNAUTHORIZED);
    }

    // Generate tokens and set cookies
    const { accessToken, accessTokenExp, refreshToken, refreshTokenExp } = await generateTokens(user);

    const userData = userSerializer(user, { blacklist: ['_id', 'roles', 'profile'] });
    return {
      ...userData,
      accessToken,
      accessTokenExp,
      refreshToken,
      refreshTokenExp,
    };
  }

  static async adminLogin(req, res) {
    if (!req.body) {
      throw new ValidationError(
        ValidationMessages.GENERAL.INVALID_REQUEST_BODY,
        HttpStatus.STATUS_CODE.UNPROCESSABLE_ENTITY
      );
    }

    const { email, password } = req.body;

    if (!email || !password) {
      throw new ValidationError('Email and password are required fields');
    }

    // Check if the user exists
    const user = await userModel.findOne({ email });
    if (!user) {
      throw new NotFoundError(ValidationMessages.AUTHENTICATION.ACCOUNT_NOT_FOUND);
    }

    // Check if the user is Admin
    if (user.roles !== UserRoles.ADMIN) {
      throw new AppError(ValidationMessages.AUTHENTICATION.INSUFFICIENT_PERMISSIONS, HttpStatus.STATUS_CODE.FORBIDDEN);
    }

    // Check if the user is deactivated
    if (!user.isVerified) {
      throw new AppError(ValidationMessages.AUTHENTICATION.ACCOUNT_NOT_VERIFIED, HttpStatus.STATUS_CODE.UNAUTHORIZED);
    }

    // Check if the user is deactivated
    if (user.isDeactivated) {
      throw new AppError(ValidationMessages.AUTHENTICATION.DEACTIVATED, HttpStatus.STATUS_CODE.FORBIDDEN);
    }

    // Check if the password is correct
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new AppError(ValidationMessages.AUTHENTICATION.INVALID_CREDENTIALS, HttpStatus.STATUS_CODE.UNAUTHORIZED);
    }

    // Generate tokens and set cookies
    const { accessToken, accessTokenExp, refreshToken, refreshTokenExp } = await generateTokens(user);

    const userData = userSerializer(user, { blacklist: ['_id', 'roles', 'profile'] });
    return {
      ...userData,
      accessToken,
      accessTokenExp,
      refreshToken,
      refreshTokenExp,
    };
  }

  static async forgotPassword(req, res) {
    if (!req.body) {
      throw new ValidationError(
        ValidationMessages.GENERAL.INVALID_REQUEST_BODY,
        HttpStatus.STATUS_CODE.UNPROCESSABLE_ENTITY
      );
    }

    const { email } = req.body;

    if (!email) {
      throw new ValidationError(ValidationMessages.AUTHENTICATION.EMAIL_REQUIRED);
    }

    const user = await userModel.findOne({ email });
    if (!user) {
      throw new AppError(ValidationMessages.AUTHENTICATION.ACCOUNT_NOT_FOUND, HttpStatus.STATUS_CODE.BAD_REQUEST);
    }

    // Delete any existing unused OTPs for this user
    await passwordResetModel.deleteMany({
      userId: user._id,
      used: false,
    });

    // Generate OTP
    const otp = generateOtp();

    // Save OTP
    await passwordResetModel.create({
      userId: user._id,
      otp,
    });

    // Send OTP email
    await sendResetPasswordEmail(user, otp);

    return null;
  }

  static async resetPassword(req, res) {
    if (!req.body) {
      throw new ValidationError(
        ValidationMessages.GENERAL.INVALID_REQUEST_BODY,
        HttpStatus.STATUS_CODE.UNPROCESSABLE_ENTITY
      );
    }

    const { email, otp, password, password_confirmation } = req.body;

    if (!email || !otp || !password || !password_confirmation) {
      throw new ValidationError('Email, OTP, password and password confirmation are required fields');
    }

    if (password !== password_confirmation) {
      throw new ValidationError('Password and password confirmation do not match');
    }

    // Check if the user exists
    const user = await userModel.findOne({ email });
    if (!user) {
      throw new NotFoundError(ValidationMessages.AUTHENTICATION.ACCOUNT_NOT_FOUND);
    }

    // Check if the OTP is valid
    const otpRecord = await passwordResetModel.findOne({
      userId: user._id,
      otp,
      used: false,
    });

    if (!otpRecord) {
      throw new AppError(ValidationMessages.AUTHENTICATION.INVALID_OTP, HttpStatus.STATUS_CODE.BAD_REQUEST);
    }

    // Hash the new password
    const saltRounds = parseInt(config.salt) || 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Update the user's password
    await userModel.updateOne({ _id: user._id }, { password: hashedPassword });

    // Mark the OTP as used
    await passwordResetModel.updateOne({ _id: otpRecord._id }, { used: true });

    return null;
  }

  static async refreshToken(req, res) {
    const { refreshToken } = req.cookies;

    if (!refreshToken) {
      throw new AppError(ValidationMessages.TOKEN.REFRESH_TOKEN_REQUIRED, HttpStatus.STATUS_CODE.UNAUTHORIZED);
    }

    // Verify the refresh token
    const decoded = jwt.verify(refreshToken, config.jwt_refresh_token_secret_key, {
      algorithms: [config.jwt_algorithm],
    });

    // Check if the user exists
    const user = await userModel.findById(decoded._id);
    if (!user) {
      throw new NotFoundError(ValidationMessages.AUTHENTICATION.ACCOUNT_NOT_FOUND);
    }

    // Generate new tokens
    const { accessToken, accessTokenExp, refreshToken: newRefreshToken, refreshTokenExp } = await generateTokens(user);

    return {
      ...userSerializer(user, { blacklist: ['_id', 'roles', 'profile'] }),
      accessToken,
      accessTokenExp,
      refreshToken: newRefreshToken,
      refreshTokenExp,
    };
  }

  static async changePassword(req, res) {
    if (!req.body) {
      throw new ValidationError(
        ValidationMessages.GENERAL.INVALID_REQUEST_BODY,
        HttpStatus.STATUS_CODE.UNPROCESSABLE_ENTITY
      );
    }

    const { current_password, new_password, new_password_confirmation } = req.body;

    if (!current_password || !new_password || !new_password_confirmation) {
      throw new ValidationError('Current password, new password and new password confirmation are required fields');
    }

    if (new_password !== new_password_confirmation) {
      throw new ValidationError('New password and new password confirmation do not match');
    }

    // Check if the user exists
    const user = await userModel.findById(req.user._id);
    if (!user) {
      throw new NotFoundError(ValidationMessages.AUTHENTICATION.ACCOUNT_NOT_FOUND);
    }

    // Check if the old password is correct
    const isPasswordValid = await bcrypt.compare(current_password, user.password);
    if (!isPasswordValid) {
      throw new AppError(
        ValidationMessages.AUTHENTICATION.OLD_PASSWORD_NOT_MATCHED,
        HttpStatus.STATUS_CODE.UNAUTHORIZED
      );
    }

    // Hash the new password
    const saltRounds = parseInt(config.salt) || 10;
    const hashedPassword = await bcrypt.hash(new_password, saltRounds);

    // Update the user's password
    await userModel.updateOne({ _id: user._id }, { password: hashedPassword });

    await invalidateSession(req, res);

    return null;
  }

  static async logout(req, res) {
    const { refreshToken } = req.cookies;

    if (!refreshToken) {
      throw new AppError(ValidationMessages.TOKEN.REFRESH_TOKEN_REQUIRED, HttpStatus.STATUS_CODE.UNAUTHORIZED);
    }

    await userRefreshTokenModel.findOneAndUpdate({ token: refreshToken }, { $set: { blacklisted: true } });

    return null;
  }
}

export default AuthService;
