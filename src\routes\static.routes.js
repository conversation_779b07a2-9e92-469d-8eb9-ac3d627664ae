import express from 'express';

const router = express.Router();

/**
 * @route   GET /
 * @desc    Serve the main welcome/landing page
 * @access  Public
 */
router.get('/', (req, res) => {
  res.sendFile('index.html', { root: 'public' });
});

/**
 * @route   GET /health
 * @desc    Serve the health check dashboard page
 * @access  Public
 */
router.get('/health', (req, res) => {
  res.sendFile('health.html', { root: 'public' });
});

/**
 * @route   GET /status
 * @desc    Redirect to health dashboard
 * @access  Public
 */
router.get('/status', (req, res) => {
  res.redirect('/health');
});

/**
 * @route   GET /monitor
 * @desc    Redirect to health dashboard
 * @access  Public
 */
router.get('/monitor', (req, res) => {
  res.redirect('/health');
});

/**
 * @route   GET /dashboard
 * @desc    Redirect to main page (client dashboard would be separate)
 * @access  Public
 */
router.get('/dashboard', (req, res) => {
  res.redirect('/');
});

export default router;
