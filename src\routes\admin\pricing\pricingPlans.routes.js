import express from 'express';
import validateAccessToken from '../../../middlewares/validateAccessToken.js';
import authenticateJWT from '../../../middlewares/authenticateJWT.js';
import authorizeRoles from '../../../middlewares/authorizeRoles.js';
import { UserRoles } from '../../../constants/userRoles.js';
import {
  createPricingPlan,
  getPricingPlans,
  getPricingPlanById,
  updatePricingPlan,
  deletePricingPlan,
} from '../../../controllers/pricing/pricingPlans.controller.js';
import { validateMongoId } from '../../../middlewares/validateMongoId.js';

const router = express.Router();

// Global middleware for all routes: Require valid access token and JWT
router.use(validateAccessToken, authenticateJWT, authorizeRoles(UserRoles.ADMIN));

/**
 * @route   POST /api/pricing/plans
 * @desc    Create a new pricing plan
 * @access  Admin only
 */
router.post('/', createPricingPlan);

/**
 * @route   GET /api/pricing/plans
 * @desc    Get all pricing plans
 * @access  Admin only
 */
router.get('/', getPricingPlans);

/**
 * @route   GET /api/pricing/plans/:id
 * @desc    Get a specific pricing plan by ID
 * @access  Admin only
 */
router.get('/:id', validateMongoId(), getPricingPlanById);

/**
 * @route   PUT /api/pricing/plans/:id
 * @desc    Update a specific pricing plan by ID
 * @access  Admin only
 */
router.put('/:id', validateMongoId(), updatePricingPlan);

/**
 * @route   DELETE /api/pricing/plans/:id
 * @desc    Delete a specific pricing plan by ID
 * @access  Admin only
 */
router.delete('/:id', validateMongoId(), deletePricingPlan);

export default router;
