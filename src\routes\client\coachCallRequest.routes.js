import express from 'express';
import validateAccessToken from '../../middlewares/validateAccessToken.js';
import authenticateJWT from '../../middlewares/authenticateJWT.js';
import { createCoachCallRequest } from '../../controllers/coachCallRequest.controller.js';

const router = express.Router();
router.use(validateAccessToken, authenticateJWT);

/**
 * @route   POST /api/clients/coach-call-request/create
 * @desc    Create a coach call request
 * @access  Client only
 */
router.post('/create', createCoachCallRequest);

export default router;
