import express from 'express';
import validateAccessToken from '../../middlewares/validateAccessToken.js';
import authenticateJWT from '../../middlewares/authenticateJWT.js';
import {
  createCoachCallRequest,
  getUserCoachCallRequests,
  getCoachCallUsageSummary,
  cancelCoachCallRequest,
} from '../../controllers/coachCallRequest.controller.js';

const router = express.Router();
router.use(validateAccessToken, authenticateJWT);

/**
 * @route   GET /api/clients/coach-call-request
 * @desc    Get user's coach call requests and usage summary
 * @access  Client only
 */
router.get('/', getUserCoachCallRequests);

/**
 * @route   GET /api/clients/coach-call-request/usage
 * @desc    Get user's coach call usage summary
 * @access  Client only
 */
router.get('/usage', getCoachCallUsageSummary);

/**
 * @route   POST /api/clients/coach-call-request/create
 * @desc    Create a coach call request
 * @access  Client only
 */
router.post('/create', createCoachCallRequest);

/**
 * @route   PATCH /api/clients/coach-call-request/:requestId/cancel
 * @desc    Cancel a coach call request
 * @access  Client only
 */
router.patch('/:requestId/cancel', cancelCoachCallRequest);

export default router;
