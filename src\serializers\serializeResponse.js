// serializeResponse.js
const defaultBlacklist = ['__v', 'password', 'isDeactivated'];

export const serializeDocument = (doc, options = {}) => {
  if (!doc) return null;

  const { blacklist = [], allow = [] } = options;

  const json = typeof doc.toObject === 'function' ? doc.toObject({ virtuals: true }) : { ...doc };

  const effectiveBlacklist = new Set([...defaultBlacklist, ...blacklist]);

  // Allow-list: remove specific fields from blacklist
  for (const field of allow) {
    effectiveBlacklist.delete(field);
  }

  for (const field of effectiveBlacklist) {
    delete json[field];
  }

  return json;
};
