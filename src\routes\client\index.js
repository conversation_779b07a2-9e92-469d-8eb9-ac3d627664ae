import express from 'express';

// Import client route modules
import profileRoutes from './profile.routes.js';
import sleepTrackingRoutes from './sleep-tracking.routes.js';
import paymentRoutes from './payment.routes.js';
import subscriptionRoutes from './subscription.routes.js';
import couponRoutes from './coupon.routes.js';
import coachCallRequestRoutes from './coachCallRequest.routes.js';
import healthChartRoutes from './healthChart.routes.js';
import featureUsageRoutes from './featureUsage.routes.js';
import notificationRoutes from './notification.routes.js';
import onboardingRoutes from './onboarding.routes.js';

const router = express.Router();

/**
 * ===== CLIENT API ROUTES =====
 * All routes require client authentication and authorization
 */

// Client profile and settings management
router.use('/profile', profileRoutes);

// Health tracking features
router.use('/sleep-tracking', sleepTrackingRoutes);

// Payment and subscription management
router.use('/payments', paymentRoutes);

// Subscription management
router.use('/subscriptions', subscriptionRoutes);

// Coupon validation
router.use('/coupons', couponRoutes);

// Coach call request
router.use('/coach-call-request', coachCallRequestRoutes);

// Health charts
router.use('/health-charts', healthChartRoutes);

// Feature usage tracking
router.use('/feature-usage', featureUsageRoutes);

// Notifications
router.use('/notifications', notificationRoutes);

// Onboarding
router.use('/onboarding', onboardingRoutes);

export default router;
