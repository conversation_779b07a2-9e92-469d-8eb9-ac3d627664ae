<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Food for Soul Tech - Health Monitoring Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="css/styles.css" />
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar">
      <div class="nav-container">
        <div class="nav-brand">
          <i class="fas fa-heart"></i>
          <span>Food for Soul Tech</span>
        </div>
        <div class="nav-links">
          <a href="#overview" class="nav-link">Overview</a>
          <a href="#health" class="nav-link">Health Monitor</a>
          <a href="#metrics" class="nav-link">Live Metrics</a>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-container">
        <div class="hero-content">
          <h1 class="hero-title">
            <span class="gradient-text">Food for Soul Tech</span>
            <br />System Health Observatory
          </h1>
          <p class="hero-description">
            Advanced real-time monitoring and diagnostics platform for comprehensive system health analysis. Track
            performance metrics, monitor service availability, analyze system resources, and ensure optimal platform
            reliability with intelligent alerting and detailed analytics.
          </p>
        </div>
        <div class="hero-visual">
          <div class="system-status-preview">
            <div class="status-indicator" id="hero-system-indicator">
              <div class="status-dot online"></div>
              <span>System Operational</span>
            </div>
            <div class="metrics-preview">
              <div class="metric-preview">
                <i class="fas fa-server"></i>
                <span id="hero-api-status">APIs Online</span>
              </div>
              <div class="metric-preview">
                <i class="fas fa-database"></i>
                <span id="hero-db-status">Database Connected</span>
              </div>
              <div class="metric-preview">
                <i class="fas fa-memory"></i>
                <span id="hero-memory-status">Memory: Normal</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Health Check Section -->
    <section id="health" class="health">
      <div class="container">
        <h2 class="section-title">Comprehensive System Health Dashboard</h2>

        <!-- Primary Health Indicators -->
        <div class="primary-health-grid">
          <div class="primary-health-card">
            <div class="health-header">
              <div class="health-icon">
                <i class="fas fa-server"></i>
              </div>
              <div class="health-info">
                <h3>System Status</h3>
                <div class="health-status online" id="system-status">
                  <i class="fas fa-circle"></i>
                  <span>Operational</span>
                </div>
              </div>
            </div>
            <div class="health-metrics">
              <div class="metric-row">
                <span class="metric-label">Overall Health</span>
                <span class="metric-value" id="overall-health">Excellent</span>
              </div>
              <div class="metric-row">
                <span class="metric-label">Active Services</span>
                <span class="metric-value" id="active-services">All</span>
              </div>
            </div>
          </div>

          <div class="primary-health-card">
            <div class="health-header">
              <div class="health-icon">
                <i class="fas fa-tachometer-alt"></i>
              </div>
              <div class="health-info">
                <h3>Performance</h3>
                <div class="health-status online" id="performance-status">
                  <i class="fas fa-circle"></i>
                  <span>Optimal</span>
                </div>
              </div>
            </div>
            <div class="health-metrics">
              <div class="metric-row">
                <span class="metric-label">Avg Response Time</span>
                <span class="metric-value" id="response-time">--ms</span>
              </div>
              <div class="metric-row">
                <span class="metric-label">Memory Usage</span>
                <span class="metric-value" id="memory-value">--</span>
              </div>
            </div>
          </div>

          <div class="primary-health-card">
            <div class="health-header">
              <div class="health-icon">
                <i class="fas fa-database"></i>
              </div>
              <div class="health-info">
                <h3>Database</h3>
                <div class="health-status online" id="database-status">
                  <i class="fas fa-circle"></i>
                  <span>Connected</span>
                </div>
              </div>
            </div>
            <div class="health-metrics">
              <div class="metric-row">
                <span class="metric-label">Connection Pool</span>
                <span class="metric-value" id="connection-pool">Active</span>
              </div>
              <div class="metric-row">
                <span class="metric-label">Query Performance</span>
                <span class="metric-value" id="query-performance">Optimal</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Live System Metrics -->
        <div class="live-metrics-dashboard">
          <h3>Live System Metrics</h3>
          <div class="metrics-grid">
            <div class="metric-card">
              <div class="metric-icon">
                <i class="fas fa-clock"></i>
              </div>
              <div class="metric-content">
                <span class="metric-label">Server Uptime</span>
                <span class="metric-value" id="uptime-value">--</span>
              </div>
            </div>
            <div class="metric-card">
              <div class="metric-icon">
                <i class="fas fa-memory"></i>
              </div>
              <div class="metric-content">
                <span class="metric-label">Memory Usage</span>
                <span class="metric-value" id="memory-usage">--</span>
              </div>
            </div>
            <div class="metric-card">
              <div class="metric-icon">
                <i class="fas fa-wifi"></i>
              </div>
              <div class="metric-content">
                <span class="metric-label">API Status</span>
                <span class="metric-value" id="api-status">--</span>
              </div>
            </div>
            <div class="metric-card">
              <div class="metric-icon">
                <i class="fas fa-heartbeat"></i>
              </div>
              <div class="metric-content">
                <span class="metric-label">Health Score</span>
                <span class="metric-value" id="health-score">--</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-brand">
            <i class="fas fa-heart"></i>
            <span>Food for Soul Tech</span>
          </div>
          <div class="footer-links">
            <a href="#overview">Overview</a>
            <a href="#health">Health Monitor</a>
            <a href="#metrics">Live Metrics</a>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2024 Food for Soul Tech. All rights reserved.</p>
        </div>
      </div>
    </footer>

    <script src="js/script.js"></script>
  </body>
</html>
