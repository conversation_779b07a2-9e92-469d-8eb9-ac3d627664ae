import emailFooter from '../emailFooter.js';

const invoiceEmailTemplate = ({ user, order, paymentDetails, coupon, originalAmount }) => {
  return `
  <!DOCTYPE html>
  <html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Payment Invoice</title>
  </head>
  <body style="margin: 0; padding: 0; background-color: #f4f4f4;">
    <table cellpadding="0" cellspacing="0" width="100%">
      <tr>
        <td align="center" style="padding: 40px 20px;">
          <table cellpadding="0" cellspacing="0" width="800" style="background: #fff; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); padding: 40px;">
            
            <tr>
              <td align="center" style="padding-bottom: 20px;">
                <h1 style="color: #333; font-size: 24px;">Thank You – Payment Received</h1>
              </td>
            </tr>

            <tr>
              <td style="font-size: 16px; color: #555; line-height: 1.6;">
                <p>Hi ${user.firstName} ${user.lastName},</p>
                <p>Thank you for your purchase! Please find your invoice details below:</p>

                <h3 style="margin-top: 20px;">Plan Details</h3>
                <table cellpadding="8" cellspacing="0" width="100%" style="border-collapse: collapse;">
                  <tr>
                    <td style="border: 1px solid #ddd;"><strong>Plan Name:</strong></td>
                    <td style="border: 1px solid #ddd;">${order.planId.name}</td>
                  </tr>
                  <tr>
                    <td style="border: 1px solid #ddd;"><strong>Description:</strong></td>
                    <td style="border: 1px solid #ddd;">${order.planId.description}</td>
                  </tr>
                  <tr>
                    <td style="border: 1px solid #ddd;"><strong>Duration:</strong></td>
                    <td style="border: 1px solid #ddd;">${order.durationId.label} (${order.durationId.valueInDays} days)</td>
                  </tr>
                </table>

                ${
                  coupon
                    ? `
                  <h3 style="margin-top: 20px;">Coupon Applied</h3>
                  <table cellpadding="8" cellspacing="0" width="100%" style="border-collapse: collapse;">
                    <tr>
                      <td style="border: 1px solid #ddd;"><strong>Coupon Code:</strong></td>
                      <td style="border: 1px solid #ddd;">${coupon.code}</td>
                    </tr>
                    <tr>
                      <td style="border: 1px solid #ddd;"><strong>Discount:</strong></td>
                      <td style="border: 1px solid #ddd;">₹${(originalAmount - order.amount).toFixed(2)}</td>
                    </tr>
                  </table>
                `
                    : ''
                }

                <h3 style="margin-top: 20px;">Payment Summary</h3>
                <table cellpadding="8" cellspacing="0" width="100%" style="border-collapse: collapse;">
                  <tr>
                    <td style="border: 1px solid #ddd;"><strong>Order ID:</strong></td>
                    <td style="border: 1px solid #ddd;">${order._id}</td>
                  </tr>
                  <tr>
                    <td style="border: 1px solid #ddd;"><strong>Payment ID:</strong></td>
                    <td style="border: 1px solid #ddd;">${order.razorpay_payment_id}</td>
                  </tr>
                  <tr>
                    <td style="border: 1px solid #ddd;"><strong>Original Amount:</strong></td>
                    <td style="border: 1px solid #ddd;">₹${originalAmount.toFixed(2)}</td>
                  </tr>
                  <tr>
                    <td style="border: 1px solid #ddd;"><strong>Amount Paid:</strong></td>
                    <td style="border: 1px solid #ddd;">₹${order.amount.toFixed(2)}</td>
                  </tr>
                  <tr>
                    <td style="border: 1px solid #ddd;"><strong>Payment Date:</strong></td>
                    <td style="border: 1px solid #ddd;">${new Date(order.paymentDate).toLocaleString()}</td>
                  </tr>
                </table>

                <p style="margin-top: 20px;">If you have any questions, feel free to contact our support team.</p>
                <p>Thank you for choosing us!</p>
              </td>
            </tr>
            ${emailFooter()}
          </table>
        </td>
      </tr>
    </table>
  </body>
  </html>
  `;
};

export default invoiceEmailTemplate;
