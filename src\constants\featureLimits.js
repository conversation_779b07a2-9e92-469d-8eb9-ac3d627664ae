/**
 * Feature limits and configuration for free and subscription-based features.
 * This file centralizes all feature limitation constants to maintain consistency
 * across the application and make it easy to modify limits.
 */

import { FreeFeatureType } from './enums.js';

/**
 * Free feature usage limits for non-subscribed users.
 * These limits define how many times a free user can access specific features.
 */
export class FreeFeatureLimits {
  /** Maximum number of free coach calls per user */
  static FREE_COACH_CALL_LIMIT = 1;

  /** Maximum number of free advice charts per user */
  static FREE_ADVICE_CHART_LIMIT = 1;

  /** 
   * Feature limit mapping for easy lookup
   * Maps feature types to their respective limits
   */
  static LIMITS = Object.freeze({
    [FreeFeatureType.FREE_COACH_CALL]: FreeFeatureLimits.FREE_COACH_CALL_LIMIT,
    [FreeFeatureType.FREE_ADVICE_CHART]: FreeFeatureLimits.FREE_ADVICE_CHART_LIMIT,
  });

  /**
   * Get the usage limit for a specific feature type
   * @param {string} featureType - The feature type from FreeFeatureType
   * @returns {number} The usage limit for the feature
   */
  static getLimit(featureType) {
    return FreeFeatureLimits.LIMITS[featureType] || 0;
  }

  /**
   * Check if a feature type has a defined limit
   * @param {string} featureType - The feature type to check
   * @returns {boolean} True if the feature has a defined limit
   */
  static hasLimit(featureType) {
    return featureType in FreeFeatureLimits.LIMITS;
  }
}

// Freeze the class to prevent modifications
Object.freeze(FreeFeatureLimits);

/**
 * Subscription feature configuration.
 * Defines which features are available to subscribed users.
 */
export class SubscriptionFeatures {
  /** Features available to subscribed users */
  static PREMIUM_FEATURES = Object.freeze([
    'unlimited_coach_calls',
    'unlimited_health_charts',
    'priority_support',
    'advanced_analytics',
    'custom_meal_plans',
    'workout_tracking',
  ]);

  /** Features that require active subscription */
  static SUBSCRIPTION_REQUIRED_FEATURES = Object.freeze([
    'coach_calls_unlimited',
    'health_charts_unlimited',
    'advanced_progress_tracking',
    'personalized_recommendations',
  ]);

  /**
   * Check if a feature requires an active subscription
   * @param {string} feature - The feature to check
   * @returns {boolean} True if the feature requires subscription
   */
  static requiresSubscription(feature) {
    return SubscriptionFeatures.SUBSCRIPTION_REQUIRED_FEATURES.includes(feature);
  }

  /**
   * Check if a feature is available to premium users
   * @param {string} feature - The feature to check
   * @returns {boolean} True if the feature is available to premium users
   */
  static isPremiumFeature(feature) {
    return SubscriptionFeatures.PREMIUM_FEATURES.includes(feature);
  }
}

// Freeze the class to prevent modifications
Object.freeze(SubscriptionFeatures);

/**
 * Feature access configuration and utility methods.
 * Provides centralized logic for determining feature access.
 */
export class FeatureAccess {
  /** Grace period for expired subscriptions (in days) */
  static SUBSCRIPTION_GRACE_PERIOD_DAYS = 3;

  /** Cache duration for feature access checks (in minutes) */
  static ACCESS_CHECK_CACHE_DURATION = 15;

  /**
   * Feature priority levels for access control
   */
  static FEATURE_PRIORITY = Object.freeze({
    HIGH: 'high',
    MEDIUM: 'medium',
    LOW: 'low',
  });

  /**
   * Feature categories for organization
   */
  static FEATURE_CATEGORIES = Object.freeze({
    COMMUNICATION: 'communication',
    HEALTH_TRACKING: 'health_tracking',
    ANALYTICS: 'analytics',
    SUPPORT: 'support',
  });

  /**
   * Feature mapping with categories and priorities
   */
  static FEATURE_CONFIG = Object.freeze({
    [FreeFeatureType.FREE_COACH_CALL]: {
      category: FeatureAccess.FEATURE_CATEGORIES.COMMUNICATION,
      priority: FeatureAccess.FEATURE_PRIORITY.HIGH,
      description: 'One-time free consultation with a coach',
    },
    [FreeFeatureType.FREE_ADVICE_CHART]: {
      category: FeatureAccess.FEATURE_CATEGORIES.HEALTH_TRACKING,
      priority: FeatureAccess.FEATURE_PRIORITY.MEDIUM,
      description: 'Free health advice chart from coach',
    },
  });

  /**
   * Get feature configuration
   * @param {string} featureType - The feature type
   * @returns {object} Feature configuration object
   */
  static getFeatureConfig(featureType) {
    return FeatureAccess.FEATURE_CONFIG[featureType] || null;
  }

  /**
   * Get features by category
   * @param {string} category - The feature category
   * @returns {array} Array of feature types in the category
   */
  static getFeaturesByCategory(category) {
    return Object.keys(FeatureAccess.FEATURE_CONFIG).filter(
      (featureType) => FeatureAccess.FEATURE_CONFIG[featureType].category === category
    );
  }

  /**
   * Get features by priority
   * @param {string} priority - The feature priority
   * @returns {array} Array of feature types with the specified priority
   */
  static getFeaturesByPriority(priority) {
    return Object.keys(FeatureAccess.FEATURE_CONFIG).filter(
      (featureType) => FeatureAccess.FEATURE_CONFIG[featureType].priority === priority
    );
  }
}

// Freeze the class to prevent modifications
Object.freeze(FeatureAccess);

/**
 * Error messages related to feature access and limitations.
 */
export class FeatureAccessMessages {
  static FEATURE_LIMIT_EXCEEDED = 'You have reached the limit for this free feature. Please upgrade to continue.';
  static SUBSCRIPTION_REQUIRED = 'This feature requires an active subscription. Please upgrade your plan.';
  static FEATURE_NOT_AVAILABLE = 'This feature is not available for your current plan.';
  static USAGE_LIMIT_WARNING = 'You are approaching the limit for this free feature.';
  static SUBSCRIPTION_EXPIRED = 'Your subscription has expired. Please renew to continue using premium features.';
  static FEATURE_DISABLED = 'This feature is currently disabled.';
  static INVALID_FEATURE_TYPE = 'Invalid feature type specified.';
  static ACCESS_DENIED = 'Access denied for this feature.';

  /**
   * Get a formatted message for feature limit exceeded
   * @param {string} featureType - The feature type
   * @param {number} limit - The usage limit
   * @returns {string} Formatted message
   */
  static getFeatureLimitMessage(featureType, limit) {
    const config = FeatureAccess.getFeatureConfig(featureType);
    const featureName = config ? config.description : featureType;
    return `You have used all ${limit} of your free ${featureName}. Upgrade to get unlimited access.`;
  }

  /**
   * Get a formatted warning message for approaching limit
   * @param {string} featureType - The feature type
   * @param {number} remaining - Remaining usage count
   * @returns {string} Formatted warning message
   */
  static getUsageWarningMessage(featureType, remaining) {
    const config = FeatureAccess.getFeatureConfig(featureType);
    const featureName = config ? config.description : featureType;
    return `You have ${remaining} free ${featureName} remaining. Consider upgrading for unlimited access.`;
  }
}

// Freeze the class to prevent modifications
Object.freeze(FeatureAccessMessages);
