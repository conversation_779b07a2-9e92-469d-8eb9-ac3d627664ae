import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals';
import request from 'supertest';
import mongoose from 'mongoose';
import app from '../../src/app.js';
import { FreeFeatureType, HealthChartType } from '../../src/constants/enums.js';
import UserFeatureUsageModel from '../../src/models/userFeatureUsage.model.js';
import HealthChartModel from '../../src/models/healthChart.model.js';
import subscriptionModel from '../../src/models/order/subscription.model.js';
import userModel from '../../src/models/user.model.js';
import clientModel from '../../src/models/client.modal.js';
import coachModel from '../../src/models/coach.model.js';

describe('Health Chart Integration Tests', () => {
  let clientUser, coachUser, clientProfile, coachProfile;
  let clientToken, coachToken;

  beforeAll(async () => {
    // Connect to test database
    if (mongoose.connection.readyState === 0) {
      await mongoose.connect(process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/ffs_test');
    }
  });

  afterAll(async () => {
    // Clean up and close database connection
    await mongoose.connection.dropDatabase();
    await mongoose.connection.close();
  });

  beforeEach(async () => {
    // Create test users
    clientUser = await userModel.create({
      firstName: 'Test',
      lastName: 'Client',
      email: '<EMAIL>',
      password: 'hashedpassword',
      roles: 'Client',
      isVerified: true,
    });

    coachUser = await userModel.create({
      firstName: 'Test',
      lastName: 'Coach',
      email: '<EMAIL>',
      password: 'hashedpassword',
      roles: 'Coach',
      isVerified: true,
    });

    // Create client profile
    clientProfile = await clientModel.create({
      userId: clientUser._id,
      weight: 70,
      height: 175,
      age: 30,
      gender: 'Male',
    });

    // Create coach profile
    coachProfile = await coachModel.create({
      userId: coachUser._id,
      specializations: ['nutrition', 'fitness'],
      experience: 5,
      isActive: true,
      isVerified: true,
    });

    // Mock JWT tokens (in real tests, you'd generate actual tokens)
    clientToken = 'mock_client_token';
    coachToken = 'mock_coach_token';
  });

  afterEach(async () => {
    // Clean up test data
    await userModel.deleteMany({});
    await clientModel.deleteMany({});
    await coachModel.deleteMany({});
    await HealthChartModel.deleteMany({});
    await UserFeatureUsageModel.deleteMany({});
    await subscriptionModel.deleteMany({});
  });

  describe('POST /api/coaches/health-charts', () => {
    it('should create a health chart successfully', async () => {
      const chartData = {
        title: 'Test Health Chart',
        description: 'A test health chart',
        chartType: HealthChartType.ADVICE_CHART,
        clientUserId: clientUser._id,
        content: {
          textContent: 'This is test advice',
          recommendations: [
            {
              category: 'nutrition',
              recommendation: 'Eat more vegetables',
              priority: 'high',
            },
          ],
        },
      };

      const response = await request(app)
        .post('/api/coaches/health-charts')
        .set('Authorization', `Bearer ${coachToken}`)
        .send(chartData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.title).toBe(chartData.title);
      expect(response.body.data.chartType).toBe(chartData.chartType);
      expect(response.body.data.isPublished).toBe(false);
    });

    it('should fail to create chart with invalid data', async () => {
      const invalidData = {
        title: '', // Empty title
        chartType: 'invalid_type',
        clientUserId: 'invalid_id',
      };

      const response = await request(app)
        .post('/api/coaches/health-charts')
        .set('Authorization', `Bearer ${coachToken}`)
        .send(invalidData)
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/coaches/health-charts/free-advice', () => {
    it('should create free advice chart for eligible client', async () => {
      const chartData = {
        title: 'Free Advice Chart',
        description: 'Free health advice for new client',
        clientUserId: clientUser._id,
        content: {
          textContent: 'Welcome! Here is your free health advice.',
        },
      };

      const response = await request(app)
        .post('/api/coaches/health-charts/free-advice')
        .set('Authorization', `Bearer ${coachToken}`)
        .send(chartData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.isFreeChart).toBe(true);
      expect(response.body.data.isPublished).toBe(true);

      // Verify feature usage was recorded
      const usage = await UserFeatureUsageModel.findOne({
        userId: clientUser._id,
        featureType: FreeFeatureType.FREE_ADVICE_CHART,
      });
      expect(usage.usageCount).toBe(1);
      expect(usage.isExhausted).toBe(true);
    });

    it('should fail to create second free advice chart', async () => {
      // First chart
      await request(app)
        .post('/api/coaches/health-charts/free-advice')
        .set('Authorization', `Bearer ${coachToken}`)
        .send({
          title: 'First Free Chart',
          clientUserId: clientUser._id,
          content: { textContent: 'First advice' },
        })
        .expect(201);

      // Second chart should fail
      const response = await request(app)
        .post('/api/coaches/health-charts/free-advice')
        .set('Authorization', `Bearer ${coachToken}`)
        .send({
          title: 'Second Free Chart',
          clientUserId: clientUser._id,
          content: { textContent: 'Second advice' },
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('already has a free advice chart');
    });
  });

  describe('PATCH /api/coaches/health-charts/:chartId/publish', () => {
    it('should publish a health chart', async () => {
      // Create a chart first
      const chart = await HealthChartModel.create({
        title: 'Test Chart',
        chartType: HealthChartType.ADVICE_CHART,
        coachId: coachProfile._id,
        clientId: clientProfile._id,
        clientUserId: clientUser._id,
        content: { textContent: 'Test content' },
        isActive: true,
        isPublished: false,
      });

      const response = await request(app)
        .patch(`/api/coaches/health-charts/${chart._id}/publish`)
        .set('Authorization', `Bearer ${coachToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.isPublished).toBe(true);
      expect(response.body.data.publishedAt).toBeDefined();
    });

    it('should fail to publish non-existent chart', async () => {
      const fakeId = new mongoose.Types.ObjectId();

      const response = await request(app)
        .patch(`/api/coaches/health-charts/${fakeId}/publish`)
        .set('Authorization', `Bearer ${coachToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/clients/health-charts', () => {
    beforeEach(async () => {
      // Create some test charts
      await HealthChartModel.create([
        {
          title: 'Published Chart 1',
          chartType: HealthChartType.ADVICE_CHART,
          coachId: coachProfile._id,
          clientId: clientProfile._id,
          clientUserId: clientUser._id,
          content: { textContent: 'Content 1' },
          isActive: true,
          isPublished: true,
          publishedAt: new Date(),
        },
        {
          title: 'Published Chart 2',
          chartType: HealthChartType.NUTRITION_CHART,
          coachId: coachProfile._id,
          clientId: clientProfile._id,
          clientUserId: clientUser._id,
          content: { textContent: 'Content 2' },
          isActive: true,
          isPublished: true,
          publishedAt: new Date(),
        },
        {
          title: 'Unpublished Chart',
          chartType: HealthChartType.ADVICE_CHART,
          coachId: coachProfile._id,
          clientId: clientProfile._id,
          clientUserId: clientUser._id,
          content: { textContent: 'Content 3' },
          isActive: true,
          isPublished: false,
        },
      ]);
    });

    it('should return accessible charts for free user', async () => {
      const response = await request(app)
        .get('/api/clients/health-charts')
        .set('Authorization', `Bearer ${clientToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.charts).toHaveLength(2); // Only published charts
      expect(response.body.data.accessInfo.hasActiveSubscription).toBe(false);
    });

    it('should filter charts by type', async () => {
      const response = await request(app)
        .get('/api/clients/health-charts?chartType=nutrition_chart')
        .set('Authorization', `Bearer ${clientToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.charts).toHaveLength(1);
      expect(response.body.data.charts[0].chartType).toBe(HealthChartType.NUTRITION_CHART);
    });

    it('should return all charts for subscribed user', async () => {
      // Create active subscription
      await subscriptionModel.create({
        userId: clientUser._id,
        planId: new mongoose.Types.ObjectId(),
        durationId: new mongoose.Types.ObjectId(),
        isActive: true,
        status: 'active',
        startDate: new Date(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      });

      const response = await request(app)
        .get('/api/clients/health-charts')
        .set('Authorization', `Bearer ${clientToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.accessInfo.hasActiveSubscription).toBe(true);
    });
  });

  describe('GET /api/clients/health-charts/:chartId', () => {
    let testChart;

    beforeEach(async () => {
      testChart = await HealthChartModel.create({
        title: 'Test Chart Detail',
        chartType: HealthChartType.ADVICE_CHART,
        coachId: coachProfile._id,
        clientId: clientProfile._id,
        clientUserId: clientUser._id,
        content: {
          textContent: 'Detailed content',
          recommendations: [
            {
              category: 'nutrition',
              recommendation: 'Eat healthy',
              priority: 'high',
            },
          ],
        },
        isActive: true,
        isPublished: true,
        publishedAt: new Date(),
      });
    });

    it('should return chart details and increment view count', async () => {
      const initialViewCount = testChart.viewCount;

      const response = await request(app)
        .get(`/api/clients/health-charts/${testChart._id}`)
        .set('Authorization', `Bearer ${clientToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.title).toBe(testChart.title);
      expect(response.body.data.content).toBeDefined();

      // Check view count was incremented
      const updatedChart = await HealthChartModel.findById(testChart._id);
      expect(updatedChart.viewCount).toBe(initialViewCount + 1);
    });

    it('should fail to access non-existent chart', async () => {
      const fakeId = new mongoose.Types.ObjectId();

      const response = await request(app)
        .get(`/api/clients/health-charts/${fakeId}`)
        .set('Authorization', `Bearer ${clientToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
    });

    it('should fail to access chart requiring subscription for free user', async () => {
      // Update chart to require subscription
      await HealthChartModel.findByIdAndUpdate(testChart._id, {
        requiresSubscription: true,
      });

      const response = await request(app)
        .get(`/api/clients/health-charts/${testChart._id}`)
        .set('Authorization', `Bearer ${clientToken}`)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('subscription');
    });
  });

  describe('GET /api/coaches/health-charts', () => {
    beforeEach(async () => {
      // Create charts for the coach
      await HealthChartModel.create([
        {
          title: 'Coach Chart 1',
          chartType: HealthChartType.ADVICE_CHART,
          coachId: coachProfile._id,
          clientId: clientProfile._id,
          clientUserId: clientUser._id,
          content: { textContent: 'Content 1' },
          isActive: true,
          isPublished: true,
        },
        {
          title: 'Coach Chart 2',
          chartType: HealthChartType.NUTRITION_CHART,
          coachId: coachProfile._id,
          clientId: clientProfile._id,
          clientUserId: clientUser._id,
          content: { textContent: 'Content 2' },
          isActive: true,
          isPublished: false,
        },
      ]);
    });

    it('should return all charts for coach', async () => {
      const response = await request(app)
        .get('/api/coaches/health-charts')
        .set('Authorization', `Bearer ${coachToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.charts).toHaveLength(2);
    });

    it('should filter charts by status', async () => {
      const response = await request(app)
        .get('/api/coaches/health-charts?status=published')
        .set('Authorization', `Bearer ${coachToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.charts).toHaveLength(1);
      expect(response.body.data.charts[0].isPublished).toBe(true);
    });
  });
});
