import express from 'express';
import validateAccessToken from '../../middlewares/validateAccessToken.js';
import authenticateJWT from '../../middlewares/authenticateJWT.js';
import authorizeRoles from '../../middlewares/authorizeRoles.js';
import { UserRoles } from '../../constants/userRoles.js';
import {
  getFeatureUsageSummary,
  getFeatureUsageDetails,
  checkFeatureAccess,
  getFeatureUsageStatistics,
} from '../../controllers/featureUsage.controller.js';

const router = express.Router();

// Apply global middlewares: JWT + Role-Based Authorization for Clients
router.use(validateAccessToken, authenticateJWT, authorizeRoles(UserRoles.CLIENT));

/**
 * @route   GET /api/clients/feature-usage
 * @desc    Get user's feature usage summary
 * @access  Client only
 */
router.get('/', getFeatureUsageSummary);

/**
 * @route   GET /api/clients/feature-usage/statistics
 * @desc    Get feature usage statistics for analytics
 * @access  Client only
 */
router.get('/statistics', getFeatureUsageStatistics);

/**
 * @route   GET /api/clients/feature-usage/:featureType
 * @desc    Get specific feature usage details
 * @access  Client only
 */
router.get('/:featureType', getFeatureUsageDetails);

/**
 * @route   GET /api/clients/feature-usage/:featureType/check-access
 * @desc    Check if user can use a specific feature
 * @access  Client only
 */
router.get('/:featureType/check-access', checkFeatureAccess);

export default router;
