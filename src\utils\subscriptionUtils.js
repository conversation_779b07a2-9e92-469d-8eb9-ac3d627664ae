import subscriptionModel from '../models/order/subscription.model.js';
import UserFeatureUsageModel from '../models/userFeatureUsage.model.js';
import { FreeFeatureType, SubscriptionStatus } from '../constants/enums.js';
import { FeatureAccess } from '../constants/featureLimits.js';

/**
 * Subscription validation and feature access utilities
 */
class SubscriptionUtils {
  /**
   * Check if user has an active subscription
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Subscription status and details
   */
  static async checkActiveSubscription(userId) {
    const subscription = await subscriptionModel.findOne({
      userId,
      isActive: true,
      status: SubscriptionStatus.ACTIVE,
      endDate: { $gt: new Date() },
    }).populate([
      {
        path: 'planId',
        select: 'name slug description',
      },
      {
        path: 'durationId',
        select: 'label valueInDays',
      },
    ]);

    return {
      hasActiveSubscription: !!subscription,
      subscription: subscription || null,
      isExpired: subscription ? new Date() > subscription.endDate : false,
      daysRemaining: subscription ? 
        Math.ceil((subscription.endDate - new Date()) / (1000 * 60 * 60 * 24)) : 0,
    };
  }

  /**
   * Get user's subscription history
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Subscription history
   */
  static async getSubscriptionHistory(userId, options = {}) {
    const { limit = 10, page = 1, status = null } = options;
    const skip = (page - 1) * limit;

    const query = { userId };
    if (status) {
      query.status = status;
    }

    const subscriptions = await subscriptionModel
      .find(query)
      .populate([
        {
          path: 'planId',
          select: 'name slug description',
        },
        {
          path: 'durationId',
          select: 'label valueInDays price currency',
        },
      ])
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await subscriptionModel.countDocuments(query);

    return {
      subscriptions,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(total / limit),
        totalSubscriptions: total,
        hasNextPage: skip + subscriptions.length < total,
        hasPrevPage: page > 1,
      },
    };
  }

  /**
   * Check if subscription is about to expire
   * @param {string} userId - User ID
   * @param {number} warningDays - Days before expiration to warn (default: 7)
   * @returns {Promise<Object>} Expiration warning info
   */
  static async checkSubscriptionExpiration(userId, warningDays = 7) {
    const subscriptionInfo = await this.checkActiveSubscription(userId);

    if (!subscriptionInfo.hasActiveSubscription) {
      return {
        isExpiring: false,
        isExpired: true,
        daysRemaining: 0,
        shouldWarn: false,
      };
    }

    const { subscription, daysRemaining } = subscriptionInfo;
    const isExpiring = daysRemaining <= warningDays && daysRemaining > 0;
    const isExpired = daysRemaining <= 0;

    return {
      isExpiring,
      isExpired,
      daysRemaining,
      shouldWarn: isExpiring,
      subscription,
    };
  }

  /**
   * Get comprehensive feature access status for a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Complete feature access information
   */
  static async getFeatureAccessStatus(userId) {
    // Check subscription status
    const subscriptionInfo = await this.checkActiveSubscription(userId);
    const { hasActiveSubscription, subscription } = subscriptionInfo;

    // Get feature usage for all free features
    const featureUsage = await UserFeatureUsageModel.getUserFeatureUsage(userId);

    // Build feature access map
    const featureAccess = {};
    
    for (const featureType of FreeFeatureType.ALL) {
      const usage = featureUsage.find(u => u.featureType === featureType);
      
      if (hasActiveSubscription) {
        // Unlimited access for subscribed users
        featureAccess[featureType] = {
          hasAccess: true,
          accessType: 'subscription',
          unlimited: true,
          usageCount: usage ? usage.usageCount : 0,
          usageLimit: null,
          remainingUsage: null,
          canUse: true,
        };
      } else {
        // Limited access for free users
        const usageInfo = await UserFeatureUsageModel.checkFeatureAccess(userId, featureType);
        featureAccess[featureType] = {
          hasAccess: usageInfo.canUse,
          accessType: 'free',
          unlimited: false,
          usageCount: usageInfo.usageCount,
          usageLimit: usageInfo.usageLimit,
          remainingUsage: usageInfo.remainingUsage,
          canUse: usageInfo.canUse,
          status: usageInfo.status,
          lastUsedAt: usageInfo.lastUsedAt,
        };
      }
    }

    return {
      subscription: subscriptionInfo,
      featureAccess,
      overallStatus: hasActiveSubscription ? 'premium' : 'free',
      upgradeRecommended: !hasActiveSubscription && this.shouldRecommendUpgrade(featureAccess),
    };
  }

  /**
   * Determine if upgrade should be recommended based on usage patterns
   * @param {Object} featureAccess - Feature access information
   * @returns {boolean} Whether to recommend upgrade
   */
  static shouldRecommendUpgrade(featureAccess) {
    let exhaustedFeatures = 0;
    let totalFeatures = 0;

    for (const [featureType, access] of Object.entries(featureAccess)) {
      if (access.accessType === 'free') {
        totalFeatures++;
        if (!access.canUse || access.remainingUsage === 0) {
          exhaustedFeatures++;
        }
      }
    }

    // Recommend upgrade if more than 50% of features are exhausted
    return exhaustedFeatures / totalFeatures > 0.5;
  }

  /**
   * Validate feature access for a specific feature
   * @param {string} userId - User ID
   * @param {string} featureType - Feature type to validate
   * @returns {Promise<Object>} Validation result
   */
  static async validateFeatureAccess(userId, featureType) {
    // Validate feature type
    if (!FreeFeatureType.ALL.includes(featureType)) {
      return {
        isValid: false,
        hasAccess: false,
        error: `Invalid feature type: ${featureType}`,
      };
    }

    // Check subscription status
    const subscriptionInfo = await this.checkActiveSubscription(userId);

    if (subscriptionInfo.hasActiveSubscription) {
      return {
        isValid: true,
        hasAccess: true,
        accessType: 'subscription',
        unlimited: true,
        subscription: subscriptionInfo.subscription,
      };
    }

    // Check free usage limits
    const usageInfo = await UserFeatureUsageModel.checkFeatureAccess(userId, featureType);

    return {
      isValid: true,
      hasAccess: usageInfo.canUse,
      accessType: 'free',
      unlimited: false,
      usageInfo,
      requiresUpgrade: !usageInfo.canUse,
    };
  }

  /**
   * Get subscription upgrade recommendations
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Upgrade recommendations
   */
  static async getUpgradeRecommendations(userId) {
    const featureStatus = await this.getFeatureAccessStatus(userId);

    if (featureStatus.subscription.hasActiveSubscription) {
      return {
        shouldUpgrade: false,
        reason: 'User already has active subscription',
        recommendations: [],
      };
    }

    const recommendations = [];
    const { featureAccess } = featureStatus;

    // Analyze usage patterns
    for (const [featureType, access] of Object.entries(featureAccess)) {
      if (access.accessType === 'free' && !access.canUse) {
        const config = FeatureAccess.getFeatureConfig(featureType);
        recommendations.push({
          featureType,
          reason: 'Feature limit exhausted',
          description: config ? config.description : featureType,
          priority: config ? config.priority : 'medium',
        });
      }
    }

    return {
      shouldUpgrade: recommendations.length > 0,
      reason: recommendations.length > 0 ? 'Multiple features exhausted' : 'No upgrade needed',
      recommendations,
      benefits: [
        'Unlimited coach calls',
        'Unlimited health charts',
        'Priority support',
        'Advanced analytics',
        'Custom meal plans',
      ],
    };
  }

  /**
   * Record subscription-related event
   * @param {string} userId - User ID
   * @param {string} eventType - Type of event
   * @param {Object} eventData - Event data
   * @returns {Promise<void>}
   */
  static async recordSubscriptionEvent(userId, eventType, eventData = {}) {
    // This could be extended to log subscription events for analytics
    console.log(`Subscription event for user ${userId}: ${eventType}`, eventData);
  }

  /**
   * Get subscription analytics for a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Subscription analytics
   */
  static async getSubscriptionAnalytics(userId) {
    const subscriptionHistory = await this.getSubscriptionHistory(userId, { limit: 100 });
    const featureStatus = await this.getFeatureAccessStatus(userId);

    // Calculate subscription metrics
    const totalSubscriptions = subscriptionHistory.subscriptions.length;
    const activeSubscriptions = subscriptionHistory.subscriptions.filter(
      sub => sub.status === SubscriptionStatus.ACTIVE
    ).length;

    // Calculate feature usage metrics
    const featureUsageStats = {};
    for (const [featureType, access] of Object.entries(featureStatus.featureAccess)) {
      featureUsageStats[featureType] = {
        usageCount: access.usageCount,
        usageRate: access.usageLimit ? (access.usageCount / access.usageLimit) * 100 : 0,
        isExhausted: !access.canUse && access.accessType === 'free',
      };
    }

    return {
      subscription: {
        totalSubscriptions,
        activeSubscriptions,
        hasActiveSubscription: featureStatus.subscription.hasActiveSubscription,
        currentPlan: featureStatus.subscription.subscription?.planId?.name || 'Free',
      },
      featureUsage: featureUsageStats,
      recommendations: await this.getUpgradeRecommendations(userId),
    };
  }
}

export default SubscriptionUtils;
