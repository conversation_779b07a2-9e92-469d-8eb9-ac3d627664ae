import { ValidationError, NotFoundError, ForbiddenError } from '../utils/errorHandler.js';
import { ValidationMessages } from '../constants/messages.js';
import { HttpStatus } from '../constants/httpStatus.js';
import { FreeFeatureType, HealthChartType, HealthChartAccessStatus } from '../constants/enums.js';
import { FeatureAccessMessages } from '../constants/featureLimits.js';
import HealthChartModel from '../models/healthChart.model.js';
import UserFeatureUsageModel from '../models/userFeatureUsage.model.js';
import subscriptionModel from '../models/order/subscription.model.js';
import clientModel from '../models/client.modal.js';
import coachModel from '../models/coach.model.js';
import userModel from '../models/user.model.js';

class HealthChartService {
  /**
   * Check if user has access to health chart features
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Access information
   */
  static async checkHealthChartAccess(userId) {
    // Check if user has active subscription
    const activeSubscription = await subscriptionModel.findOne({
      userId,
      isActive: true,
      status: 'active',
      endDate: { $gt: new Date() },
    });

    if (activeSubscription) {
      return {
        hasAccess: true,
        accessType: 'subscription',
        unlimited: true,
        subscription: activeSubscription,
      };
    }

    // Check free usage limits for advice chart
    const usageInfo = await UserFeatureUsageModel.checkFeatureAccess(userId, FreeFeatureType.FREE_ADVICE_CHART);

    return {
      hasAccess: usageInfo.canUse,
      accessType: 'free',
      unlimited: false,
      usageInfo,
    };
  }

  /**
   * Create a health chart (Coach only)
   * @param {Object} req - Express request object
   * @returns {Promise<Object>} Created health chart
   */
  static async createHealthChart(req) {
    const coachUserId = req.user.id || req.user._id;
    const {
      title,
      description,
      chartType,
      content,
      clientUserId,
      requiresSubscription = false,
      isFreeChart = false,
      expiresAt = null,
      tags = [],
      priority = 'medium',
    } = req.body;

    // Validate required fields
    if (!title || !chartType || !clientUserId) {
      throw new ValidationError('Title, chart type, and client user ID are required');
    }

    // Validate chart type
    if (!HealthChartType.ALL.includes(chartType)) {
      throw new ValidationError(`Invalid chart type: ${chartType}`);
    }

    // Get coach profile
    const coach = await coachModel.findOne({ userId: coachUserId });
    if (!coach) {
      throw new NotFoundError('Coach profile not found');
    }

    // Get client profile
    const client = await clientModel.findOne({ userId: clientUserId });
    if (!client) {
      throw new NotFoundError('Client not found');
    }

    // Validate client user exists
    const clientUser = await userModel.findById(clientUserId);
    if (!clientUser) {
      throw new NotFoundError('Client user not found');
    }

    // Create health chart
    const healthChart = new HealthChartModel({
      title,
      description,
      chartType,
      content: content || {},
      coachId: coach._id,
      clientId: client._id,
      clientUserId,
      requiresSubscription,
      isFreeChart,
      expiresAt,
      tags,
      priority,
      isActive: true,
      isPublished: false, // Charts need to be published explicitly
    });

    await healthChart.save();

    // Increment coach's charts sent count
    await coach.incrementChartsSent();

    return {
      id: healthChart._id,
      title: healthChart.title,
      description: healthChart.description,
      chartType: healthChart.chartType,
      content: healthChart.content,
      requiresSubscription: healthChart.requiresSubscription,
      isFreeChart: healthChart.isFreeChart,
      accessStatus: healthChart.accessStatus,
      isPublished: healthChart.isPublished,
      createdAt: healthChart.createdAt,
    };
  }

  /**
   * Publish a health chart (Coach only)
   * @param {Object} req - Express request object
   * @returns {Promise<Object>} Published health chart
   */
  static async publishHealthChart(req) {
    const coachUserId = req.user.id || req.user._id;
    const { chartId } = req.params;

    // Get coach profile
    const coach = await coachModel.findOne({ userId: coachUserId });
    if (!coach) {
      throw new NotFoundError('Coach profile not found');
    }

    // Find the health chart
    const healthChart = await HealthChartModel.findOne({
      _id: chartId,
      coachId: coach._id,
    });

    if (!healthChart) {
      throw new NotFoundError('Health chart not found or you do not have permission to publish it');
    }

    // Publish the chart
    await healthChart.publish();

    // If it's a free advice chart, record the usage
    if (healthChart.isFreeChart && healthChart.chartType === HealthChartType.ADVICE_CHART) {
      const clientUserId = healthChart.clientUserId;
      
      // Check if user can still use free advice chart
      const accessInfo = await this.checkHealthChartAccess(clientUserId);
      
      if (accessInfo.accessType === 'free' && accessInfo.hasAccess) {
        await UserFeatureUsageModel.recordUsage(clientUserId, FreeFeatureType.FREE_ADVICE_CHART, {
          entityType: 'HealthChart',
          entityId: healthChart._id,
        });
      }
    }

    return {
      id: healthChart._id,
      title: healthChart.title,
      isPublished: healthChart.isPublished,
      publishedAt: healthChart.publishedAt,
      message: 'Health chart published successfully',
    };
  }

  /**
   * Get health charts for a client
   * @param {Object} req - Express request object
   * @returns {Promise<Object>} Health charts and access info
   */
  static async getClientHealthCharts(req) {
    const clientUserId = req.user.id || req.user._id;
    const { chartType, page = 1, limit = 10 } = req.query;

    // Check user's access level
    const accessInfo = await this.checkHealthChartAccess(clientUserId);

    // Build filters
    const filters = {};
    if (chartType && HealthChartType.ALL.includes(chartType)) {
      filters.chartType = chartType;
    }

    // Get accessible charts
    const charts = await HealthChartModel.getAccessibleCharts(
      clientUserId,
      accessInfo.unlimited,
      filters
    );

    // Paginate results
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedCharts = charts.slice(startIndex, endIndex);

    // Format charts for response
    const formattedCharts = paginatedCharts.map(chart => ({
      id: chart._id,
      title: chart.title,
      description: chart.description,
      chartType: chart.chartType,
      content: chart.content,
      coachName: chart.coachId ? `${chart.coachId.firstName} ${chart.coachId.lastName}` : 'Unknown Coach',
      requiresSubscription: chart.requiresSubscription,
      isFreeChart: chart.isFreeChart,
      accessStatus: chart.accessStatus,
      priority: chart.priority,
      tags: chart.tags,
      viewCount: chart.viewCount,
      createdAt: chart.createdAt,
      publishedAt: chart.publishedAt,
    }));

    return {
      charts: formattedCharts,
      pagination: {
        currentPage: parseInt(page),
        totalCharts: charts.length,
        totalPages: Math.ceil(charts.length / limit),
        hasNextPage: endIndex < charts.length,
        hasPrevPage: page > 1,
      },
      accessInfo: {
        hasActiveSubscription: accessInfo.unlimited,
        accessType: accessInfo.accessType,
        canAccessFreeChart: accessInfo.hasAccess,
        usageInfo: accessInfo.usageInfo || null,
      },
    };
  }

  /**
   * Get a specific health chart for a client
   * @param {Object} req - Express request object
   * @returns {Promise<Object>} Health chart details
   */
  static async getHealthChartById(req) {
    const clientUserId = req.user.id || req.user._id;
    const { chartId } = req.params;

    // Check user's access level
    const accessInfo = await this.checkHealthChartAccess(clientUserId);

    // Find the health chart
    const healthChart = await HealthChartModel.findOne({
      _id: chartId,
      clientUserId,
    }).populate('coachId', 'firstName lastName email');

    if (!healthChart) {
      throw new NotFoundError('Health chart not found');
    }

    // Check if chart is accessible
    if (!healthChart.isAccessible(accessInfo.unlimited)) {
      if (healthChart.requiresSubscription && !accessInfo.unlimited) {
        throw new ForbiddenError(FeatureAccessMessages.SUBSCRIPTION_REQUIRED);
      } else {
        throw new ForbiddenError('This health chart is not accessible');
      }
    }

    // Increment view count
    await healthChart.incrementViewCount();

    return {
      id: healthChart._id,
      title: healthChart.title,
      description: healthChart.description,
      chartType: healthChart.chartType,
      content: healthChart.content,
      coach: healthChart.coachId ? {
        name: `${healthChart.coachId.firstName} ${healthChart.coachId.lastName}`,
        email: healthChart.coachId.email,
      } : null,
      requiresSubscription: healthChart.requiresSubscription,
      isFreeChart: healthChart.isFreeChart,
      accessStatus: healthChart.accessStatus,
      priority: healthChart.priority,
      tags: healthChart.tags,
      viewCount: healthChart.viewCount,
      lastViewedAt: healthChart.lastViewedAt,
      createdAt: healthChart.createdAt,
      publishedAt: healthChart.publishedAt,
      expiresAt: healthChart.expiresAt,
    };
  }

  /**
   * Get coach's health charts
   * @param {Object} req - Express request object
   * @returns {Promise<Array>} Coach's health charts
   */
  static async getCoachHealthCharts(req) {
    const coachUserId = req.user.id || req.user._id;
    const { page = 1, limit = 10, status = 'all' } = req.query;

    // Get coach profile
    const coach = await coachModel.findOne({ userId: coachUserId });
    if (!coach) {
      throw new NotFoundError('Coach profile not found');
    }

    // Build query
    const query = { coachId: coach._id };
    
    if (status === 'published') {
      query.isPublished = true;
    } else if (status === 'draft') {
      query.isPublished = false;
    }

    // Get charts with pagination
    const skip = (page - 1) * limit;
    const charts = await HealthChartModel.find(query)
      .populate('clientUserId', 'firstName lastName email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const totalCharts = await HealthChartModel.countDocuments(query);

    // Format charts for response
    const formattedCharts = charts.map(chart => ({
      id: chart._id,
      title: chart.title,
      description: chart.description,
      chartType: chart.chartType,
      clientName: chart.clientUserId ? 
        `${chart.clientUserId.firstName} ${chart.clientUserId.lastName}` : 'Unknown Client',
      clientEmail: chart.clientUserId?.email,
      requiresSubscription: chart.requiresSubscription,
      isFreeChart: chart.isFreeChart,
      isPublished: chart.isPublished,
      accessStatus: chart.accessStatus,
      priority: chart.priority,
      viewCount: chart.viewCount,
      createdAt: chart.createdAt,
      publishedAt: chart.publishedAt,
    }));

    return {
      charts: formattedCharts,
      pagination: {
        currentPage: parseInt(page),
        totalCharts,
        totalPages: Math.ceil(totalCharts / limit),
        hasNextPage: skip + charts.length < totalCharts,
        hasPrevPage: page > 1,
      },
    };
  }

  /**
   * Create a free advice chart for a client
   * @param {Object} req - Express request object
   * @returns {Promise<Object>} Created free advice chart
   */
  static async createFreeAdviceChart(req) {
    const coachUserId = req.user.id || req.user._id;
    const { title, description, content, clientUserId } = req.body;

    // Validate required fields
    if (!title || !clientUserId) {
      throw new ValidationError('Title and client user ID are required');
    }

    // Check if client already has a free advice chart
    const existingFreeChart = await HealthChartModel.getFreeAdviceChart(clientUserId);
    if (existingFreeChart) {
      throw new ValidationError('Client already has a free advice chart');
    }

    // Check if client can still use free advice chart
    const accessInfo = await this.checkHealthChartAccess(clientUserId);
    if (accessInfo.accessType === 'free' && !accessInfo.hasAccess) {
      throw new ForbiddenError('Client has already used their free advice chart');
    }

    // Get coach profile
    const coach = await coachModel.findOne({ userId: coachUserId });
    if (!coach) {
      throw new NotFoundError('Coach profile not found');
    }

    // Get client profile
    const client = await clientModel.findOne({ userId: clientUserId });
    if (!client) {
      throw new NotFoundError('Client not found');
    }

    // Create free advice chart
    const freeChart = await HealthChartModel.createFreeAdviceChart({
      title,
      description,
      content: content || {},
      coachId: coach._id,
      clientId: client._id,
      clientUserId,
      isActive: true,
      isPublished: true, // Auto-publish free charts
      publishedAt: new Date(),
    });

    // Record feature usage
    if (accessInfo.accessType === 'free') {
      await UserFeatureUsageModel.recordUsage(clientUserId, FreeFeatureType.FREE_ADVICE_CHART, {
        entityType: 'HealthChart',
        entityId: freeChart._id,
      });
    }

    // Increment coach's charts sent count
    await coach.incrementChartsSent();

    return {
      id: freeChart._id,
      title: freeChart.title,
      description: freeChart.description,
      chartType: freeChart.chartType,
      isFreeChart: freeChart.isFreeChart,
      isPublished: freeChart.isPublished,
      message: 'Free advice chart created and published successfully',
    };
  }
}

export default HealthChartService;
