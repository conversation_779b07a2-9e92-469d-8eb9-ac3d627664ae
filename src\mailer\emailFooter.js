/**
 * Generates the HTML for the email footer.
 * @returns {string} - HTML string for the email footer.
 */
const emailFooter = () => {
  const year = new Date().getFullYear();
  return `
    <tr>
      <td style="padding-top: 30px; font-size: 14px; color: #999; text-align: center;">
        &copy; ${year} Food For Soul. All rights reserved.
      </td>
    </tr>
  `;
};

export default emailFooter;
