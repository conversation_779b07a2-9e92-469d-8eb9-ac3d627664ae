import mongoose from 'mongoose';

const WaterRecordSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    consumedLiters: {
      type: Number,
      required: true,
      min: 0,
      max: 12,
      validate: {
        validator: function (value) {
          return value >= 0 && value <= 12;
        },
        message: 'Consumed liters must be between 0 and 15',
      },
      default: 0,
    },
    date: {
      type: Date,
      default: Date.now,
    },
  },
  { timestamps: true }
);

export default mongoose.models.WaterRecord || mongoose.model('WaterRecord', WaterRecordSchema, 'water_records');
