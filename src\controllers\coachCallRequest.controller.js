import asyncHandler from '../utils/asyncHandler.js';
import { HttpStatus } from '../constants/httpStatus.js';
import CoachCallRequestService from '../services/coachCallRequest.service.js';
import successResponse from '../utils/successResponse.js';

const createCoachCallRequest = asyncHandler(async (req, res) => {
  const coachCallRequestData = await CoachCallRequestService.createCoachCallRequest(req, res);

  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Coach call request sent successfully', coachCallRequestData);
});

export { createCoachCallRequest };
