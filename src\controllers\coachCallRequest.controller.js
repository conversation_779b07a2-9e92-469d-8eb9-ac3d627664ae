import asyncHandler from '../utils/asyncHandler.js';
import { HttpStatus } from '../constants/httpStatus.js';
import CoachCallRequestService from '../services/coachCallRequest.service.js';
import successResponse from '../utils/successResponse.js';

const createCoachCallRequest = asyncHandler(async (req, res) => {
  const coachCallRequestData = await CoachCallRequestService.createCoachCallRequest(req, res);

  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Coach call request sent successfully', coachCallRequestData);
});

/**
 * Get user's coach call requests and usage summary
 * @route GET /api/clients/coach-call-request
 * @access Client
 */
const getUserCoachCallRequests = asyncHandler(async (req, res) => {
  const data = await CoachCallRequestService.getUserCoachCallRequests(req);

  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Coach call requests retrieved successfully', data);
});

/**
 * Get user's coach call usage summary
 * @route GET /api/clients/coach-call-request/usage
 * @access Client
 */
const getCoachCallUsageSummary = asyncHandler(async (req, res) => {
  const userId = req.user.id || req.user._id;
  const usageSummary = await CoachCallRequestService.getCoachCallUsageSummary(userId);

  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Coach call usage summary retrieved successfully', usageSummary);
});

/**
 * Cancel a coach call request
 * @route PATCH /api/clients/coach-call-request/:requestId/cancel
 * @access Client
 */
const cancelCoachCallRequest = asyncHandler(async (req, res) => {
  const result = await CoachCallRequestService.cancelCoachCallRequest(req);

  successResponse(res, HttpStatus.STATUS_CODE.OK, result.message, result);
});

export { createCoachCallRequest, getUserCoachCallRequests, getCoachCallUsageSummary, cancelCoachCallRequest };
