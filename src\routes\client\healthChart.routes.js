import express from 'express';
import validateAccessToken from '../../middlewares/validateAccessToken.js';
import authenticateJWT from '../../middlewares/authenticateJWT.js';
import authorizeRoles from '../../middlewares/authorizeRoles.js';
import { UserRoles } from '../../constants/userRoles.js';
import {
  getClientHealthCharts,
  getHealthChartById,
  getHealthChartAccessSummary,
} from '../../controllers/healthChart.controller.js';

const router = express.Router();

// Apply global middlewares: JWT + Role-Based Authorization for Clients
router.use(validateAccessToken, authenticateJWT, authorizeRoles(UserRoles.CLIENT));

/**
 * @route   GET /api/clients/health-charts/access-summary
 * @desc    Get client's health chart access summary
 * @access  Client only
 */
router.get('/access-summary', getHealthChartAccessSummary);

/**
 * @route   GET /api/clients/health-charts
 * @desc    Get health charts for the authenticated client
 * @access  Client only
 * @query   { chartType?, page?, limit? }
 */
router.get('/', getClientHealthCharts);

/**
 * @route   GET /api/clients/health-charts/:chartId
 * @desc    Get a specific health chart by ID
 * @access  Client only
 */
router.get('/:chartId', getHealthChartById);

export default router;
