import mongoose from 'mongoose';
import { FreeFeatureType, FeatureUsageStatus } from '../constants/enums.js';
import { FreeFeatureLimits } from '../constants/featureLimits.js';

/**
 * User Feature Usage Schema
 * Tracks individual user feature usage for free and premium features
 * Enables feature limitation and usage analytics
 */
const UserFeatureUsageSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true,
    },
    featureType: {
      type: String,
      enum: FreeFeatureType.ALL,
      required: true,
      index: true,
    },
    usageCount: {
      type: Number,
      default: 0,
      min: 0,
    },
    usageLimit: {
      type: Number,
      required: true,
      min: 0,
    },
    status: {
      type: String,
      enum: FeatureUsageStatus.ALL,
      default: FeatureUsageStatus.AVAILABLE,
      index: true,
    },
    firstUsedAt: {
      type: Date,
      default: null,
    },
    lastUsedAt: {
      type: Date,
      default: null,
    },
    isExhausted: {
      type: Boolean,
      default: false,
      index: true,
    },
    metadata: {
      type: Object,
      default: {},
    },
    // Track related entities for audit purposes
    relatedEntities: [
      {
        entityType: {
          type: String,
          enum: ['CoachCallRequest', 'HealthChart', 'Subscription'],
        },
        entityId: {
          type: mongoose.Schema.Types.ObjectId,
        },
        usedAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
  },
  {
    timestamps: true,
  }
);

// Compound indexes for efficient querying
UserFeatureUsageSchema.index({ userId: 1, featureType: 1 }, { unique: true });
UserFeatureUsageSchema.index({ userId: 1, status: 1 });
UserFeatureUsageSchema.index({ featureType: 1, status: 1 });
UserFeatureUsageSchema.index({ isExhausted: 1, featureType: 1 });

/**
 * Instance method to increment usage count
 * @param {Object} entityInfo - Information about the related entity
 * @returns {Promise<UserFeatureUsage>} Updated document
 */
UserFeatureUsageSchema.methods.incrementUsage = async function (entityInfo = {}) {
  const now = new Date();
  
  // Set first usage timestamp if this is the first use
  if (this.usageCount === 0) {
    this.firstUsedAt = now;
  }
  
  // Increment usage count
  this.usageCount += 1;
  this.lastUsedAt = now;
  
  // Add related entity information if provided
  if (entityInfo.entityType && entityInfo.entityId) {
    this.relatedEntities.push({
      entityType: entityInfo.entityType,
      entityId: entityInfo.entityId,
      usedAt: now,
    });
  }
  
  // Update status based on usage
  this.updateStatus();
  
  return this.save();
};

/**
 * Instance method to update status based on current usage
 */
UserFeatureUsageSchema.methods.updateStatus = function () {
  if (this.usageCount === 0) {
    this.status = FeatureUsageStatus.AVAILABLE;
    this.isExhausted = false;
  } else if (this.usageCount >= this.usageLimit) {
    this.status = FeatureUsageStatus.EXHAUSTED;
    this.isExhausted = true;
  } else {
    this.status = FeatureUsageStatus.USED;
    this.isExhausted = false;
  }
};

/**
 * Instance method to check if feature can be used
 * @returns {boolean} True if feature can be used
 */
UserFeatureUsageSchema.methods.canUseFeature = function () {
  return this.usageCount < this.usageLimit && this.status !== FeatureUsageStatus.DISABLED;
};

/**
 * Instance method to get remaining usage count
 * @returns {number} Remaining usage count
 */
UserFeatureUsageSchema.methods.getRemainingUsage = function () {
  return Math.max(0, this.usageLimit - this.usageCount);
};

/**
 * Instance method to reset usage (for testing or admin purposes)
 * @returns {Promise<UserFeatureUsage>} Updated document
 */
UserFeatureUsageSchema.methods.resetUsage = async function () {
  this.usageCount = 0;
  this.status = FeatureUsageStatus.AVAILABLE;
  this.isExhausted = false;
  this.firstUsedAt = null;
  this.lastUsedAt = null;
  this.relatedEntities = [];
  
  return this.save();
};

/**
 * Static method to create or get feature usage record for a user
 * @param {string} userId - User ID
 * @param {string} featureType - Feature type from FreeFeatureType
 * @returns {Promise<UserFeatureUsage>} Feature usage document
 */
UserFeatureUsageSchema.statics.getOrCreateUsageRecord = async function (userId, featureType) {
  // Validate feature type
  if (!FreeFeatureType.ALL.includes(featureType)) {
    throw new Error(`Invalid feature type: ${featureType}`);
  }
  
  // Try to find existing record
  let usageRecord = await this.findOne({ userId, featureType });
  
  // Create new record if it doesn't exist
  if (!usageRecord) {
    const usageLimit = FreeFeatureLimits.getLimit(featureType);
    usageRecord = new this({
      userId,
      featureType,
      usageLimit,
      status: FeatureUsageStatus.AVAILABLE,
    });
    await usageRecord.save();
  }
  
  return usageRecord;
};

/**
 * Static method to get all feature usage for a user
 * @param {string} userId - User ID
 * @returns {Promise<Array>} Array of feature usage documents
 */
UserFeatureUsageSchema.statics.getUserFeatureUsage = async function (userId) {
  return this.find({ userId }).sort({ featureType: 1 });
};

/**
 * Static method to check if user can use a specific feature
 * @param {string} userId - User ID
 * @param {string} featureType - Feature type
 * @returns {Promise<Object>} Object with canUse boolean and usage info
 */
UserFeatureUsageSchema.statics.checkFeatureAccess = async function (userId, featureType) {
  const usageRecord = await this.getOrCreateUsageRecord(userId, featureType);
  
  return {
    canUse: usageRecord.canUseFeature(),
    usageCount: usageRecord.usageCount,
    usageLimit: usageRecord.usageLimit,
    remainingUsage: usageRecord.getRemainingUsage(),
    status: usageRecord.status,
    isExhausted: usageRecord.isExhausted,
    lastUsedAt: usageRecord.lastUsedAt,
  };
};

/**
 * Static method to record feature usage
 * @param {string} userId - User ID
 * @param {string} featureType - Feature type
 * @param {Object} entityInfo - Related entity information
 * @returns {Promise<Object>} Updated usage information
 */
UserFeatureUsageSchema.statics.recordUsage = async function (userId, featureType, entityInfo = {}) {
  const usageRecord = await this.getOrCreateUsageRecord(userId, featureType);
  
  // Check if feature can be used
  if (!usageRecord.canUseFeature()) {
    throw new Error(`Feature usage limit exceeded for ${featureType}`);
  }
  
  // Increment usage
  await usageRecord.incrementUsage(entityInfo);
  
  return {
    usageCount: usageRecord.usageCount,
    usageLimit: usageRecord.usageLimit,
    remainingUsage: usageRecord.getRemainingUsage(),
    status: usageRecord.status,
    isExhausted: usageRecord.isExhausted,
  };
};

/**
 * Static method to get usage statistics
 * @param {string} featureType - Optional feature type filter
 * @returns {Promise<Object>} Usage statistics
 */
UserFeatureUsageSchema.statics.getUsageStatistics = async function (featureType = null) {
  const matchStage = featureType ? { featureType } : {};
  
  const stats = await this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: '$featureType',
        totalUsers: { $sum: 1 },
        usersWithUsage: { $sum: { $cond: [{ $gt: ['$usageCount', 0] }, 1, 0] } },
        exhaustedUsers: { $sum: { $cond: ['$isExhausted', 1, 0] } },
        totalUsage: { $sum: '$usageCount' },
        averageUsage: { $avg: '$usageCount' },
      },
    },
  ]);
  
  return stats;
};

// Pre-save middleware to update status
UserFeatureUsageSchema.pre('save', function (next) {
  if (this.isModified('usageCount') || this.isModified('usageLimit')) {
    this.updateStatus();
  }
  next();
});

export default mongoose.models.UserFeatureUsage || 
  mongoose.model('UserFeatureUsage', UserFeatureUsageSchema, 'user_feature_usage');
