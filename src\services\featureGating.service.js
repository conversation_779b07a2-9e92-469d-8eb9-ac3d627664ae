import { FreeFeatureType, FeatureUsageStatus } from '../constants/enums.js';
import { FeatureAccessMessages, FreeFeatureLimits, FeatureAccess } from '../constants/featureLimits.js';
import { ForbiddenError, ValidationError } from '../utils/errorHandler.js';
import SubscriptionUtils from '../utils/subscriptionUtils.js';
import UserFeatureUsageModel from '../models/userFeatureUsage.model.js';

/**
 * Feature Gating Service
 * Centralized service for managing feature access based on subscription status
 */
class FeatureGatingService {
  /**
   * Check if user can access a specific feature
   * @param {string} userId - User ID
   * @param {string} featureType - Feature type to check
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Access result
   */
  static async checkFeatureAccess(userId, featureType, options = {}) {
    const { throwOnDenied = false, recordAttempt = false } = options;

    try {
      // Validate feature type
      if (!FreeFeatureType.ALL.includes(featureType)) {
        const error = new ValidationError(`Invalid feature type: ${featureType}`);
        if (throwOnDenied) throw error;
        return { hasAccess: false, error: error.message };
      }

      // Get subscription status
      const subscriptionInfo = await SubscriptionUtils.checkActiveSubscription(userId);

      // If user has active subscription, grant access
      if (subscriptionInfo.hasActiveSubscription) {
        return {
          hasAccess: true,
          accessType: 'subscription',
          unlimited: true,
          subscription: subscriptionInfo.subscription,
          message: 'Access granted via active subscription',
        };
      }

      // Check free usage limits
      const usageInfo = await UserFeatureUsageModel.checkFeatureAccess(userId, featureType);

      if (!usageInfo.canUse) {
        const limit = FreeFeatureLimits.getLimit(featureType);
        const message = FeatureAccessMessages.getFeatureLimitMessage(featureType, limit);
        const error = new ForbiddenError(message);
        
        if (recordAttempt) {
          await this.recordAccessAttempt(userId, featureType, false, 'limit_exceeded');
        }
        
        if (throwOnDenied) throw error;
        
        return {
          hasAccess: false,
          accessType: 'free',
          unlimited: false,
          usageInfo,
          error: message,
          requiresUpgrade: true,
        };
      }

      // Access granted for free user
      if (recordAttempt) {
        await this.recordAccessAttempt(userId, featureType, true, 'free_access');
      }

      return {
        hasAccess: true,
        accessType: 'free',
        unlimited: false,
        usageInfo,
        message: 'Access granted via free usage limit',
      };

    } catch (error) {
      if (throwOnDenied) throw error;
      return {
        hasAccess: false,
        error: error.message,
      };
    }
  }

  /**
   * Gate a feature - check access and throw error if denied
   * @param {string} userId - User ID
   * @param {string} featureType - Feature type to gate
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Access information if granted
   */
  static async gateFeature(userId, featureType, options = {}) {
    return this.checkFeatureAccess(userId, featureType, {
      ...options,
      throwOnDenied: true,
      recordAttempt: true,
    });
  }

  /**
   * Record feature usage after successful access
   * @param {string} userId - User ID
   * @param {string} featureType - Feature type used
   * @param {Object} entityInfo - Related entity information
   * @returns {Promise<Object>} Updated usage information
   */
  static async recordFeatureUsage(userId, featureType, entityInfo = {}) {
    // Check if user has unlimited access
    const subscriptionInfo = await SubscriptionUtils.checkActiveSubscription(userId);
    
    if (subscriptionInfo.hasActiveSubscription) {
      // Don't record usage for subscription users, but log the activity
      await this.recordAccessAttempt(userId, featureType, true, 'subscription_usage');
      return {
        recorded: false,
        reason: 'unlimited_access',
        accessType: 'subscription',
      };
    }

    // Record usage for free users
    try {
      const updatedUsage = await UserFeatureUsageModel.recordUsage(userId, featureType, entityInfo);
      
      await this.recordAccessAttempt(userId, featureType, true, 'free_usage_recorded');
      
      return {
        recorded: true,
        accessType: 'free',
        ...updatedUsage,
      };
    } catch (error) {
      await this.recordAccessAttempt(userId, featureType, false, 'usage_recording_failed');
      throw error;
    }
  }

  /**
   * Get feature availability summary for a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Feature availability summary
   */
  static async getFeatureAvailability(userId) {
    const subscriptionInfo = await SubscriptionUtils.checkActiveSubscription(userId);
    const featureAvailability = {};

    // Check each feature type
    for (const featureType of FreeFeatureType.ALL) {
      const accessResult = await this.checkFeatureAccess(userId, featureType);
      const config = FeatureAccess.getFeatureConfig(featureType);

      featureAvailability[featureType] = {
        ...accessResult,
        config,
        priority: config?.priority || 'medium',
        category: config?.category || 'general',
        description: config?.description || featureType,
      };
    }

    return {
      subscription: subscriptionInfo,
      features: featureAvailability,
      overallAccess: subscriptionInfo.hasActiveSubscription ? 'unlimited' : 'limited',
      upgradeRecommended: await this.shouldRecommendUpgrade(userId),
    };
  }

  /**
   * Check if upgrade should be recommended for a user
   * @param {string} userId - User ID
   * @returns {Promise<boolean>} Whether upgrade is recommended
   */
  static async shouldRecommendUpgrade(userId) {
    const subscriptionInfo = await SubscriptionUtils.checkActiveSubscription(userId);
    
    if (subscriptionInfo.hasActiveSubscription) {
      return false;
    }

    // Check how many features are exhausted
    let exhaustedCount = 0;
    let totalFeatures = FreeFeatureType.ALL.length;

    for (const featureType of FreeFeatureType.ALL) {
      const usageInfo = await UserFeatureUsageModel.checkFeatureAccess(userId, featureType);
      if (!usageInfo.canUse) {
        exhaustedCount++;
      }
    }

    // Recommend upgrade if more than 50% of features are exhausted
    return (exhaustedCount / totalFeatures) > 0.5;
  }

  /**
   * Get feature usage analytics for a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Usage analytics
   */
  static async getFeatureUsageAnalytics(userId) {
    const subscriptionInfo = await SubscriptionUtils.checkActiveSubscription(userId);
    const featureUsage = await UserFeatureUsageModel.getUserFeatureUsage(userId);

    const analytics = {
      subscription: subscriptionInfo,
      totalFeatures: FreeFeatureType.ALL.length,
      featuresUsed: 0,
      featuresExhausted: 0,
      totalUsageCount: 0,
      featureBreakdown: {},
      usagePatterns: {},
    };

    // Analyze each feature
    for (const featureType of FreeFeatureType.ALL) {
      const usage = featureUsage.find(u => u.featureType === featureType);
      const limit = FreeFeatureLimits.getLimit(featureType);

      if (usage) {
        analytics.totalUsageCount += usage.usageCount;
        
        if (usage.usageCount > 0) {
          analytics.featuresUsed++;
        }
        
        if (usage.isExhausted) {
          analytics.featuresExhausted++;
        }

        analytics.featureBreakdown[featureType] = {
          usageCount: usage.usageCount,
          usageLimit: usage.usageLimit,
          usageRate: (usage.usageCount / usage.usageLimit) * 100,
          status: usage.status,
          isExhausted: usage.isExhausted,
          firstUsedAt: usage.firstUsedAt,
          lastUsedAt: usage.lastUsedAt,
          relatedEntitiesCount: usage.relatedEntities.length,
        };
      } else {
        analytics.featureBreakdown[featureType] = {
          usageCount: 0,
          usageLimit: limit,
          usageRate: 0,
          status: FeatureUsageStatus.AVAILABLE,
          isExhausted: false,
          firstUsedAt: null,
          lastUsedAt: null,
          relatedEntitiesCount: 0,
        };
      }
    }

    // Calculate usage patterns
    analytics.usagePatterns = {
      utilizationRate: (analytics.featuresUsed / analytics.totalFeatures) * 100,
      exhaustionRate: (analytics.featuresExhausted / analytics.totalFeatures) * 100,
      averageUsagePerFeature: analytics.totalUsageCount / analytics.totalFeatures,
      upgradeRecommended: await this.shouldRecommendUpgrade(userId),
    };

    return analytics;
  }

  /**
   * Bulk check feature access for multiple features
   * @param {string} userId - User ID
   * @param {Array} featureTypes - Array of feature types to check
   * @returns {Promise<Object>} Bulk access results
   */
  static async bulkCheckFeatureAccess(userId, featureTypes) {
    const results = {};
    const subscriptionInfo = await SubscriptionUtils.checkActiveSubscription(userId);

    for (const featureType of featureTypes) {
      try {
        results[featureType] = await this.checkFeatureAccess(userId, featureType);
      } catch (error) {
        results[featureType] = {
          hasAccess: false,
          error: error.message,
        };
      }
    }

    return {
      subscription: subscriptionInfo,
      results,
      overallAccess: subscriptionInfo.hasActiveSubscription ? 'unlimited' : 'limited',
    };
  }

  /**
   * Record access attempt for analytics
   * @param {string} userId - User ID
   * @param {string} featureType - Feature type
   * @param {boolean} granted - Whether access was granted
   * @param {string} reason - Reason for the result
   * @returns {Promise<void>}
   */
  static async recordAccessAttempt(userId, featureType, granted, reason) {
    // This could be extended to store access attempts in a separate collection
    // for analytics and monitoring purposes
    const logData = {
      userId,
      featureType,
      granted,
      reason,
      timestamp: new Date(),
    };

    // For now, just log to console - could be extended to database logging
    console.log('Feature access attempt:', logData);
  }

  /**
   * Get feature gate configuration
   * @param {string} featureType - Feature type
   * @returns {Object} Feature gate configuration
   */
  static getFeatureGateConfig(featureType) {
    const config = FeatureAccess.getFeatureConfig(featureType);
    const limit = FreeFeatureLimits.getLimit(featureType);

    return {
      featureType,
      limit,
      config,
      isValid: FreeFeatureType.ALL.includes(featureType),
    };
  }

  /**
   * Validate feature gate configuration
   * @returns {Object} Validation results
   */
  static validateFeatureGateConfiguration() {
    const validation = {
      isValid: true,
      errors: [],
      warnings: [],
      features: {},
    };

    for (const featureType of FreeFeatureType.ALL) {
      const config = this.getFeatureGateConfig(featureType);
      
      validation.features[featureType] = {
        hasConfig: !!config.config,
        hasLimit: config.limit > 0,
        isValid: config.isValid,
      };

      if (!config.config) {
        validation.warnings.push(`No configuration found for feature: ${featureType}`);
      }

      if (config.limit <= 0) {
        validation.errors.push(`Invalid limit for feature: ${featureType}`);
        validation.isValid = false;
      }
    }

    return validation;
  }
}

export default FeatureGatingService;
