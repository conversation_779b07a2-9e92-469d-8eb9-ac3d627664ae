import mongoose from 'mongoose';
import { CoachCallRequestStatus } from '../constants/enums.js';

const CoachCallRequestSchema = new mongoose.Schema(
  {
    userName: {
      type: String,
      required: true,
      default: null,
      trim: true,
    },
    userEmail: {
      type: String,
      required: true,
      lowercase: true,
      trim: true,
    },
    userMessage: {
      type: String,
      default: null,
      trim: true,
    },
    preferredDate: {
      type: Date,
      default: null,
      required: true,
    },
    preferredTime: {
      type: String,
      default: null,
      required: true,
    },
    status: {
      type: String,
      enum: CoachCallRequestStatus.ALL,
      default: CoachCallRequestStatus.PENDING,
    },
  },
  {
    timestamps: true,
  }
);

export const CoachCallRequest =
  mongoose.models.CoachCallRequest || mongoose.model('CoachCallRequest', CoachCallRequestSchema, 'coach_call_requests');
