import express from 'express';
import {
  getAllSubscriptions,
  getSubscriptionStats,
  toggleSubscriptionStatus,
  getSubscriptionByIdAdmin,
  updateExpiredSubscriptions,
} from '../../controllers/order/subscription.controller.js';
import validateAccessToken from '../../middlewares/validateAccessToken.js';
import authenticateJWT from '../../middlewares/authenticateJWT.js';
import authorizeRoles from '../../middlewares/authorizeRoles.js';
import { validateMongoId } from '../../middlewares/validateMongoId.js';
import { UserRoles } from '../../constants/userRoles.js';

const router = express.Router();

// Apply authentication and authorization middleware to all admin subscription routes
router.use(validateAccessToken, authenticateJWT, authorizeRoles(UserRoles.ADMIN));

/**
 * @route   GET /api/admin/subscriptions
 * @desc    Get all subscriptions with filters
 * @access  Admin only
 * @query   { page?, limit?, status?, planId?, userId?, isActive?, startDate?, endDate? }
 */
router.get('/', getAllSubscriptions);

/**
 * @route   GET /api/admin/subscriptions/stats
 * @desc    Get subscription statistics
 * @access  Admin only
 */
router.get('/stats', getSubscriptionStats);

/**
 * @route   POST /api/admin/subscriptions/update-expired
 * @desc    Update expired subscriptions
 * @access  Admin only
 */
router.post('/update-expired', updateExpiredSubscriptions);

/**
 * @route   GET /api/admin/subscriptions/:id
 * @desc    Get subscription details by ID
 * @access  Admin only
 */
router.get('/:id', validateMongoId(), getSubscriptionByIdAdmin);

/**
 * @route   PATCH /api/admin/subscriptions/:id/toggle
 * @desc    Toggle subscription status (activate/deactivate)
 * @access  Admin only
 * @body    { action: 'activate' | 'deactivate' }
 */
router.patch('/:id/toggle', validateMongoId(), toggleSubscriptionStatus);

export default router;
