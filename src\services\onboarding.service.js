import { FreeFeatureType } from '../constants/enums.js';
import { FreeFeatureLimits, FeatureAccess } from '../constants/featureLimits.js';
import { ValidationError, NotFoundError } from '../utils/errorHandler.js';
import UserFeatureUsageModel from '../models/userFeatureUsage.model.js';
import SubscriptionUtils from '../utils/subscriptionUtils.js';
import FeatureGatingService from './featureGating.service.js';
import userModel from '../models/user.model.js';
import clientModel from '../models/client.modal.js';

/**
 * Onboarding Service
 * Enhanced onboarding process with feature limitation awareness
 */
class OnboardingService {
  /**
   * Initialize user onboarding with feature setup
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Onboarding initialization result
   */
  static async initializeUserOnboarding(userId) {
    // Get user information
    const user = await userModel.findById(userId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Initialize feature usage records for all free features
    const featureInitialization = {};
    
    for (const featureType of FreeFeatureType.ALL) {
      try {
        const usageRecord = await UserFeatureUsageModel.getOrCreateUsageRecord(userId, featureType);
        featureInitialization[featureType] = {
          initialized: true,
          usageLimit: usageRecord.usageLimit,
          status: usageRecord.status,
        };
      } catch (error) {
        featureInitialization[featureType] = {
          initialized: false,
          error: error.message,
        };
      }
    }

    // Get subscription status
    const subscriptionInfo = await SubscriptionUtils.checkActiveSubscription(userId);

    // Create onboarding summary
    const onboardingSummary = {
      userId,
      userInfo: {
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        isVerified: user.isVerified,
        isOnboarded: user.isOnboarded,
      },
      subscription: subscriptionInfo,
      features: featureInitialization,
      onboardingSteps: await this.getOnboardingSteps(userId),
      recommendations: await this.getOnboardingRecommendations(userId),
    };

    return onboardingSummary;
  }

  /**
   * Get onboarding steps for a user
   * @param {string} userId - User ID
   * @returns {Promise<Array>} Onboarding steps
   */
  static async getOnboardingSteps(userId) {
    const user = await userModel.findById(userId);
    const client = await clientModel.findOne({ userId });
    const subscriptionInfo = await SubscriptionUtils.checkActiveSubscription(userId);

    const steps = [
      {
        id: 'profile_setup',
        title: 'Complete Your Profile',
        description: 'Add your personal information and health goals',
        completed: !!client && client.weight && client.height,
        required: true,
        order: 1,
      },
      {
        id: 'email_verification',
        title: 'Verify Your Email',
        description: 'Confirm your email address to secure your account',
        completed: user.isVerified,
        required: true,
        order: 2,
      },
      {
        id: 'feature_introduction',
        title: 'Discover Free Features',
        description: 'Learn about your free coach call and health chart benefits',
        completed: false, // This step is informational
        required: false,
        order: 3,
        features: await this.getFeatureIntroduction(userId),
      },
      {
        id: 'subscription_overview',
        title: 'Explore Premium Benefits',
        description: 'See what unlimited access can offer you',
        completed: subscriptionInfo.hasActiveSubscription,
        required: false,
        order: 4,
        benefits: await this.getSubscriptionBenefits(),
      },
    ];

    return steps.sort((a, b) => a.order - b.order);
  }

  /**
   * Get feature introduction for onboarding
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Feature introduction
   */
  static async getFeatureIntroduction(userId) {
    const featureIntro = {};

    for (const featureType of FreeFeatureType.ALL) {
      const config = FeatureAccess.getFeatureConfig(featureType);
      const limit = FreeFeatureLimits.getLimit(featureType);
      const usageInfo = await UserFeatureUsageModel.checkFeatureAccess(userId, featureType);

      featureIntro[featureType] = {
        title: this.getFeatureTitle(featureType),
        description: config?.description || featureType,
        limit,
        available: usageInfo.canUse,
        usageCount: usageInfo.usageCount,
        remainingUsage: usageInfo.remainingUsage,
        category: config?.category || 'general',
        priority: config?.priority || 'medium',
        howToUse: this.getFeatureUsageInstructions(featureType),
      };
    }

    return featureIntro;
  }

  /**
   * Get subscription benefits for onboarding
   * @returns {Promise<Object>} Subscription benefits
   */
  static async getSubscriptionBenefits() {
    return {
      unlimited_features: {
        title: 'Unlimited Access',
        description: 'Use all features without limits',
        features: [
          'Unlimited coach calls',
          'Unlimited health charts',
          'Priority support',
        ],
      },
      premium_features: {
        title: 'Premium Features',
        description: 'Access exclusive premium functionality',
        features: [
          'Advanced analytics',
          'Custom meal plans',
          'Workout tracking',
          'Progress monitoring',
        ],
      },
      support: {
        title: 'Priority Support',
        description: 'Get help when you need it',
        features: [
          '24/7 customer support',
          'Dedicated account manager',
          'Priority response times',
        ],
      },
    };
  }

  /**
   * Get onboarding recommendations based on user profile
   * @param {string} userId - User ID
   * @returns {Promise<Array>} Personalized recommendations
   */
  static async getOnboardingRecommendations(userId) {
    const user = await userModel.findById(userId);
    const client = await clientModel.findOne({ userId });
    const subscriptionInfo = await SubscriptionUtils.checkActiveSubscription(userId);
    const featureAvailability = await FeatureGatingService.getFeatureAvailability(userId);

    const recommendations = [];

    // Profile completion recommendation
    if (!client || !client.weight || !client.height) {
      recommendations.push({
        type: 'profile_completion',
        priority: 'high',
        title: 'Complete Your Health Profile',
        description: 'Add your health information to get personalized recommendations',
        action: 'Complete Profile',
        actionUrl: '/profile/setup',
      });
    }

    // Email verification recommendation
    if (!user.isVerified) {
      recommendations.push({
        type: 'email_verification',
        priority: 'high',
        title: 'Verify Your Email',
        description: 'Secure your account and enable all features',
        action: 'Verify Email',
        actionUrl: '/verify-email',
      });
    }

    // Free feature usage recommendation
    if (featureAvailability.features.free_coach_call.hasAccess) {
      recommendations.push({
        type: 'use_free_feature',
        priority: 'medium',
        title: 'Try Your Free Coach Call',
        description: 'Get personalized guidance from our health experts',
        action: 'Schedule Call',
        actionUrl: '/coach-call/request',
        feature: 'free_coach_call',
      });
    }

    // Subscription recommendation
    if (!subscriptionInfo.hasActiveSubscription && featureAvailability.upgradeRecommended) {
      recommendations.push({
        type: 'subscription_upgrade',
        priority: 'medium',
        title: 'Unlock Unlimited Access',
        description: 'Get unlimited coach calls and health charts',
        action: 'View Plans',
        actionUrl: '/subscription/plans',
        benefits: ['Unlimited features', 'Priority support', 'Advanced analytics'],
      });
    }

    return recommendations.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  /**
   * Complete onboarding step
   * @param {string} userId - User ID
   * @param {string} stepId - Step ID to complete
   * @param {Object} stepData - Step completion data
   * @returns {Promise<Object>} Completion result
   */
  static async completeOnboardingStep(userId, stepId, stepData = {}) {
    const user = await userModel.findById(userId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    let completionResult = {
      stepId,
      completed: false,
      message: '',
    };

    switch (stepId) {
      case 'profile_setup':
        completionResult = await this.completeProfileSetup(userId, stepData);
        break;
      
      case 'email_verification':
        completionResult = await this.completeEmailVerification(userId, stepData);
        break;
      
      case 'feature_introduction':
        completionResult = await this.completeFeatureIntroduction(userId, stepData);
        break;
      
      case 'subscription_overview':
        completionResult = await this.completeSubscriptionOverview(userId, stepData);
        break;
      
      default:
        throw new ValidationError(`Invalid onboarding step: ${stepId}`);
    }

    // Check if all required steps are completed
    const allSteps = await this.getOnboardingSteps(userId);
    const requiredSteps = allSteps.filter(step => step.required);
    const completedRequiredSteps = requiredSteps.filter(step => step.completed);

    if (completedRequiredSteps.length === requiredSteps.length && !user.isOnboarded) {
      await userModel.findByIdAndUpdate(userId, { isOnboarded: true });
      completionResult.onboardingCompleted = true;
    }

    return completionResult;
  }

  /**
   * Get feature title for display
   * @param {string} featureType - Feature type
   * @returns {string} Display title
   */
  static getFeatureTitle(featureType) {
    const titles = {
      [FreeFeatureType.FREE_COACH_CALL]: 'Free Coach Call',
      [FreeFeatureType.FREE_ADVICE_CHART]: 'Free Health Chart',
    };
    return titles[featureType] || featureType;
  }

  /**
   * Get feature usage instructions
   * @param {string} featureType - Feature type
   * @returns {string} Usage instructions
   */
  static getFeatureUsageInstructions(featureType) {
    const instructions = {
      [FreeFeatureType.FREE_COACH_CALL]: 'Click "Request Coach Call" to schedule your free consultation with a health expert.',
      [FreeFeatureType.FREE_ADVICE_CHART]: 'Your coach will send you a personalized health chart with recommendations.',
    };
    return instructions[featureType] || 'Feature usage instructions not available.';
  }

  /**
   * Complete profile setup step
   * @param {string} userId - User ID
   * @param {Object} stepData - Step data
   * @returns {Promise<Object>} Completion result
   */
  static async completeProfileSetup(userId, stepData) {
    // This would integrate with the existing profile setup logic
    return {
      stepId: 'profile_setup',
      completed: true,
      message: 'Profile setup completed successfully',
    };
  }

  /**
   * Complete email verification step
   * @param {string} userId - User ID
   * @param {Object} stepData - Step data
   * @returns {Promise<Object>} Completion result
   */
  static async completeEmailVerification(userId, stepData) {
    // This would integrate with the existing email verification logic
    return {
      stepId: 'email_verification',
      completed: true,
      message: 'Email verification completed successfully',
    };
  }

  /**
   * Complete feature introduction step
   * @param {string} userId - User ID
   * @param {Object} stepData - Step data
   * @returns {Promise<Object>} Completion result
   */
  static async completeFeatureIntroduction(userId, stepData) {
    return {
      stepId: 'feature_introduction',
      completed: true,
      message: 'Feature introduction completed',
    };
  }

  /**
   * Complete subscription overview step
   * @param {string} userId - User ID
   * @param {Object} stepData - Step data
   * @returns {Promise<Object>} Completion result
   */
  static async completeSubscriptionOverview(userId, stepData) {
    return {
      stepId: 'subscription_overview',
      completed: true,
      message: 'Subscription overview completed',
    };
  }

  /**
   * Get onboarding progress for a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Onboarding progress
   */
  static async getOnboardingProgress(userId) {
    const steps = await this.getOnboardingSteps(userId);
    const totalSteps = steps.length;
    const completedSteps = steps.filter(step => step.completed).length;
    const requiredSteps = steps.filter(step => step.required).length;
    const completedRequiredSteps = steps.filter(step => step.required && step.completed).length;

    return {
      totalSteps,
      completedSteps,
      requiredSteps,
      completedRequiredSteps,
      progressPercentage: (completedSteps / totalSteps) * 100,
      requiredProgressPercentage: (completedRequiredSteps / requiredSteps) * 100,
      isComplete: completedRequiredSteps === requiredSteps,
      nextStep: steps.find(step => !step.completed),
    };
  }
}

export default OnboardingService;
