import asyncHandler from '../utils/asyncHandler.js';
import { HttpStatus } from '../constants/httpStatus.js';
import successResponse from '../utils/successResponse.js';
import { FreeFeatureType, FeatureUsageStatus } from '../constants/enums.js';
import { FreeFeatureLimits } from '../constants/featureLimits.js';
import UserFeatureUsageModel from '../models/userFeatureUsage.model.js';
import subscriptionModel from '../models/order/subscription.model.js';

/**
 * Get user's feature usage summary
 * @route GET /api/clients/feature-usage
 * @access Client
 */
const getFeatureUsageSummary = asyncHandler(async (req, res) => {
  const userId = req.user.id || req.user._id;

  // Check if user has active subscription
  const activeSubscription = await subscriptionModel.findOne({
    userId,
    isActive: true,
    status: 'active',
    endDate: { $gt: new Date() },
  });

  const hasActiveSubscription = !!activeSubscription;

  // Get all feature usage for the user
  const featureUsage = await UserFeatureUsageModel.getUserFeatureUsage(userId);

  // Format feature usage summary
  const usageSummary = {
    hasActiveSubscription,
    subscription: hasActiveSubscription ? {
      planId: activeSubscription.planId,
      status: activeSubscription.status,
      startDate: activeSubscription.startDate,
      endDate: activeSubscription.endDate,
    } : null,
    features: {},
    overallStatus: hasActiveSubscription ? 'unlimited' : 'limited',
  };

  // Add usage info for each feature type
  for (const featureType of FreeFeatureType.ALL) {
    const usage = featureUsage.find((u) => u.featureType === featureType);
    const limit = FreeFeatureLimits.getLimit(featureType);

    if (usage) {
      usageSummary.features[featureType] = {
        usageCount: usage.usageCount,
        usageLimit: usage.usageLimit,
        remainingUsage: usage.getRemainingUsage(),
        status: usage.status,
        isExhausted: usage.isExhausted,
        canUse: hasActiveSubscription || usage.canUseFeature(),
        lastUsedAt: usage.lastUsedAt,
        firstUsedAt: usage.firstUsedAt,
      };
    } else {
      usageSummary.features[featureType] = {
        usageCount: 0,
        usageLimit: limit,
        remainingUsage: limit,
        status: FeatureUsageStatus.AVAILABLE,
        isExhausted: false,
        canUse: hasActiveSubscription || limit > 0,
        lastUsedAt: null,
        firstUsedAt: null,
      };
    }
  }

  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Feature usage summary retrieved successfully', usageSummary);
});

/**
 * Get specific feature usage details
 * @route GET /api/clients/feature-usage/:featureType
 * @access Client
 */
const getFeatureUsageDetails = asyncHandler(async (req, res) => {
  const userId = req.user.id || req.user._id;
  const { featureType } = req.params;

  // Validate feature type
  if (!FreeFeatureType.ALL.includes(featureType)) {
    return res.status(HttpStatus.STATUS_CODE.BAD_REQUEST).json({
      success: false,
      message: `Invalid feature type: ${featureType}`,
    });
  }

  // Check subscription status
  const activeSubscription = await subscriptionModel.findOne({
    userId,
    isActive: true,
    status: 'active',
    endDate: { $gt: new Date() },
  });

  const hasActiveSubscription = !!activeSubscription;

  // Get feature usage details
  const usageRecord = await UserFeatureUsageModel.getOrCreateUsageRecord(userId, featureType);

  const featureDetails = {
    featureType,
    hasActiveSubscription,
    usageCount: usageRecord.usageCount,
    usageLimit: usageRecord.usageLimit,
    remainingUsage: usageRecord.getRemainingUsage(),
    status: usageRecord.status,
    isExhausted: usageRecord.isExhausted,
    canUse: hasActiveSubscription || usageRecord.canUseFeature(),
    firstUsedAt: usageRecord.firstUsedAt,
    lastUsedAt: usageRecord.lastUsedAt,
    relatedEntities: usageRecord.relatedEntities,
    metadata: usageRecord.metadata,
  };

  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Feature usage details retrieved successfully', featureDetails);
});

/**
 * Check if user can use a specific feature
 * @route GET /api/clients/feature-usage/:featureType/check-access
 * @access Client
 */
const checkFeatureAccess = asyncHandler(async (req, res) => {
  const userId = req.user.id || req.user._id;
  const { featureType } = req.params;

  // Validate feature type
  if (!FreeFeatureType.ALL.includes(featureType)) {
    return res.status(HttpStatus.STATUS_CODE.BAD_REQUEST).json({
      success: false,
      message: `Invalid feature type: ${featureType}`,
    });
  }

  // Check feature access
  const accessInfo = await UserFeatureUsageModel.checkFeatureAccess(userId, featureType);

  // Check subscription status
  const activeSubscription = await subscriptionModel.findOne({
    userId,
    isActive: true,
    status: 'active',
    endDate: { $gt: new Date() },
  });

  const hasActiveSubscription = !!activeSubscription;

  const accessDetails = {
    featureType,
    hasAccess: hasActiveSubscription || accessInfo.canUse,
    accessType: hasActiveSubscription ? 'subscription' : 'free',
    unlimited: hasActiveSubscription,
    usageInfo: hasActiveSubscription ? null : accessInfo,
    subscription: hasActiveSubscription ? {
      status: activeSubscription.status,
      endDate: activeSubscription.endDate,
    } : null,
  };

  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Feature access check completed', accessDetails);
});

/**
 * Get feature usage statistics (for analytics)
 * @route GET /api/clients/feature-usage/statistics
 * @access Client
 */
const getFeatureUsageStatistics = asyncHandler(async (req, res) => {
  const userId = req.user.id || req.user._id;

  // Get user's feature usage
  const featureUsage = await UserFeatureUsageModel.getUserFeatureUsage(userId);

  // Calculate statistics
  const statistics = {
    totalFeatures: FreeFeatureType.ALL.length,
    featuresUsed: featureUsage.filter(usage => usage.usageCount > 0).length,
    featuresExhausted: featureUsage.filter(usage => usage.isExhausted).length,
    totalUsageCount: featureUsage.reduce((sum, usage) => sum + usage.usageCount, 0),
    featureBreakdown: {},
  };

  // Add breakdown for each feature
  for (const featureType of FreeFeatureType.ALL) {
    const usage = featureUsage.find(u => u.featureType === featureType);
    statistics.featureBreakdown[featureType] = {
      used: usage ? usage.usageCount > 0 : false,
      usageCount: usage ? usage.usageCount : 0,
      exhausted: usage ? usage.isExhausted : false,
      lastUsed: usage ? usage.lastUsedAt : null,
    };
  }

  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Feature usage statistics retrieved successfully', statistics);
});

export {
  getFeatureUsageSummary,
  getFeatureUsageDetails,
  checkFeatureAccess,
  getFeatureUsageStatistics,
};
