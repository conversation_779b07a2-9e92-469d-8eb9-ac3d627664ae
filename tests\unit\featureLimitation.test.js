import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import mongoose from 'mongoose';
import { FreeFeatureType, FeatureUsageStatus } from '../../src/constants/enums.js';
import { FreeFeatureLimits } from '../../src/constants/featureLimits.js';
import UserFeatureUsageModel from '../../src/models/userFeatureUsage.model.js';
import FeatureGatingService from '../../src/services/featureGating.service.js';
import SubscriptionUtils from '../../src/utils/subscriptionUtils.js';

// Mock dependencies
jest.mock('../../src/models/order/subscription.model.js');
jest.mock('../../src/models/userFeatureUsage.model.js');

describe('Feature Limitation System', () => {
  const mockUserId = new mongoose.Types.ObjectId();
  const mockFeatureType = FreeFeatureType.FREE_COACH_CALL;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('FreeFeatureLimits', () => {
    it('should return correct limits for each feature type', () => {
      expect(FreeFeatureLimits.getLimit(FreeFeatureType.FREE_COACH_CALL)).toBe(1);
      expect(FreeFeatureLimits.getLimit(FreeFeatureType.FREE_ADVICE_CHART)).toBe(1);
    });

    it('should return 0 for invalid feature type', () => {
      expect(FreeFeatureLimits.getLimit('invalid_feature')).toBe(0);
    });

    it('should check if feature has limit', () => {
      expect(FreeFeatureLimits.hasLimit(FreeFeatureType.FREE_COACH_CALL)).toBe(true);
      expect(FreeFeatureLimits.hasLimit('invalid_feature')).toBe(false);
    });
  });

  describe('UserFeatureUsageModel', () => {
    describe('getOrCreateUsageRecord', () => {
      it('should create new usage record if none exists', async () => {
        const mockUsageRecord = {
          userId: mockUserId,
          featureType: mockFeatureType,
          usageCount: 0,
          usageLimit: 1,
          status: FeatureUsageStatus.AVAILABLE,
          save: jest.fn().mockResolvedValue(true),
        };

        UserFeatureUsageModel.findOne = jest.fn().mockResolvedValue(null);
        UserFeatureUsageModel.prototype.constructor = jest.fn().mockReturnValue(mockUsageRecord);

        const result = await UserFeatureUsageModel.getOrCreateUsageRecord(mockUserId, mockFeatureType);

        expect(UserFeatureUsageModel.findOne).toHaveBeenCalledWith({
          userId: mockUserId,
          featureType: mockFeatureType,
        });
        expect(result).toEqual(mockUsageRecord);
      });

      it('should return existing usage record if found', async () => {
        const existingRecord = {
          userId: mockUserId,
          featureType: mockFeatureType,
          usageCount: 1,
          usageLimit: 1,
          status: FeatureUsageStatus.EXHAUSTED,
        };

        UserFeatureUsageModel.findOne = jest.fn().mockResolvedValue(existingRecord);

        const result = await UserFeatureUsageModel.getOrCreateUsageRecord(mockUserId, mockFeatureType);

        expect(result).toEqual(existingRecord);
      });

      it('should throw error for invalid feature type', async () => {
        await expect(
          UserFeatureUsageModel.getOrCreateUsageRecord(mockUserId, 'invalid_feature')
        ).rejects.toThrow('Invalid feature type: invalid_feature');
      });
    });

    describe('checkFeatureAccess', () => {
      it('should return access info for available feature', async () => {
        const mockUsageRecord = {
          canUseFeature: () => true,
          usageCount: 0,
          usageLimit: 1,
          getRemainingUsage: () => 1,
          status: FeatureUsageStatus.AVAILABLE,
          isExhausted: false,
          lastUsedAt: null,
        };

        UserFeatureUsageModel.getOrCreateUsageRecord = jest.fn().mockResolvedValue(mockUsageRecord);

        const result = await UserFeatureUsageModel.checkFeatureAccess(mockUserId, mockFeatureType);

        expect(result).toEqual({
          canUse: true,
          usageCount: 0,
          usageLimit: 1,
          remainingUsage: 1,
          status: FeatureUsageStatus.AVAILABLE,
          isExhausted: false,
          lastUsedAt: null,
        });
      });

      it('should return access info for exhausted feature', async () => {
        const mockUsageRecord = {
          canUseFeature: () => false,
          usageCount: 1,
          usageLimit: 1,
          getRemainingUsage: () => 0,
          status: FeatureUsageStatus.EXHAUSTED,
          isExhausted: true,
          lastUsedAt: new Date(),
        };

        UserFeatureUsageModel.getOrCreateUsageRecord = jest.fn().mockResolvedValue(mockUsageRecord);

        const result = await UserFeatureUsageModel.checkFeatureAccess(mockUserId, mockFeatureType);

        expect(result.canUse).toBe(false);
        expect(result.isExhausted).toBe(true);
        expect(result.remainingUsage).toBe(0);
      });
    });

    describe('recordUsage', () => {
      it('should record usage and update counts', async () => {
        const mockUsageRecord = {
          canUseFeature: () => true,
          incrementUsage: jest.fn().mockResolvedValue(true),
          usageCount: 1,
          usageLimit: 1,
          getRemainingUsage: () => 0,
          status: FeatureUsageStatus.EXHAUSTED,
          isExhausted: true,
        };

        UserFeatureUsageModel.getOrCreateUsageRecord = jest.fn().mockResolvedValue(mockUsageRecord);

        const entityInfo = { entityType: 'CoachCallRequest', entityId: new mongoose.Types.ObjectId() };
        const result = await UserFeatureUsageModel.recordUsage(mockUserId, mockFeatureType, entityInfo);

        expect(mockUsageRecord.incrementUsage).toHaveBeenCalledWith(entityInfo);
        expect(result).toEqual({
          usageCount: 1,
          usageLimit: 1,
          remainingUsage: 0,
          status: FeatureUsageStatus.EXHAUSTED,
          isExhausted: true,
        });
      });

      it('should throw error when trying to record usage for exhausted feature', async () => {
        const mockUsageRecord = {
          canUseFeature: () => false,
        };

        UserFeatureUsageModel.getOrCreateUsageRecord = jest.fn().mockResolvedValue(mockUsageRecord);

        await expect(
          UserFeatureUsageModel.recordUsage(mockUserId, mockFeatureType)
        ).rejects.toThrow(`Feature usage limit exceeded for ${mockFeatureType}`);
      });
    });
  });

  describe('FeatureGatingService', () => {
    describe('checkFeatureAccess', () => {
      it('should grant access for subscribed users', async () => {
        const mockSubscription = {
          hasActiveSubscription: true,
          subscription: { id: 'sub_123', status: 'active' },
        };

        SubscriptionUtils.checkActiveSubscription = jest.fn().mockResolvedValue(mockSubscription);

        const result = await FeatureGatingService.checkFeatureAccess(mockUserId, mockFeatureType);

        expect(result).toEqual({
          hasAccess: true,
          accessType: 'subscription',
          unlimited: true,
          subscription: mockSubscription.subscription,
          message: 'Access granted via active subscription',
        });
      });

      it('should check free usage for non-subscribed users', async () => {
        const mockSubscription = {
          hasActiveSubscription: false,
          subscription: null,
        };

        const mockUsageInfo = {
          canUse: true,
          usageCount: 0,
          usageLimit: 1,
          remainingUsage: 1,
          status: FeatureUsageStatus.AVAILABLE,
        };

        SubscriptionUtils.checkActiveSubscription = jest.fn().mockResolvedValue(mockSubscription);
        UserFeatureUsageModel.checkFeatureAccess = jest.fn().mockResolvedValue(mockUsageInfo);

        const result = await FeatureGatingService.checkFeatureAccess(mockUserId, mockFeatureType);

        expect(result).toEqual({
          hasAccess: true,
          accessType: 'free',
          unlimited: false,
          usageInfo: mockUsageInfo,
          message: 'Access granted via free usage limit',
        });
      });

      it('should deny access for exhausted free users', async () => {
        const mockSubscription = {
          hasActiveSubscription: false,
          subscription: null,
        };

        const mockUsageInfo = {
          canUse: false,
          usageCount: 1,
          usageLimit: 1,
          remainingUsage: 0,
          status: FeatureUsageStatus.EXHAUSTED,
        };

        SubscriptionUtils.checkActiveSubscription = jest.fn().mockResolvedValue(mockSubscription);
        UserFeatureUsageModel.checkFeatureAccess = jest.fn().mockResolvedValue(mockUsageInfo);

        const result = await FeatureGatingService.checkFeatureAccess(mockUserId, mockFeatureType);

        expect(result.hasAccess).toBe(false);
        expect(result.requiresUpgrade).toBe(true);
        expect(result.error).toContain('You have used all 1 of your free');
      });

      it('should handle invalid feature type', async () => {
        const result = await FeatureGatingService.checkFeatureAccess(mockUserId, 'invalid_feature');

        expect(result.hasAccess).toBe(false);
        expect(result.error).toBe('Invalid feature type: invalid_feature');
      });
    });

    describe('gateFeature', () => {
      it('should throw error when access is denied', async () => {
        const mockSubscription = {
          hasActiveSubscription: false,
          subscription: null,
        };

        const mockUsageInfo = {
          canUse: false,
          usageLimit: 1,
        };

        SubscriptionUtils.checkActiveSubscription = jest.fn().mockResolvedValue(mockSubscription);
        UserFeatureUsageModel.checkFeatureAccess = jest.fn().mockResolvedValue(mockUsageInfo);

        await expect(
          FeatureGatingService.gateFeature(mockUserId, mockFeatureType)
        ).rejects.toThrow();
      });

      it('should return access info when access is granted', async () => {
        const mockSubscription = {
          hasActiveSubscription: true,
          subscription: { id: 'sub_123' },
        };

        SubscriptionUtils.checkActiveSubscription = jest.fn().mockResolvedValue(mockSubscription);

        const result = await FeatureGatingService.gateFeature(mockUserId, mockFeatureType);

        expect(result.hasAccess).toBe(true);
        expect(result.accessType).toBe('subscription');
      });
    });

    describe('recordFeatureUsage', () => {
      it('should not record usage for subscribed users', async () => {
        const mockSubscription = {
          hasActiveSubscription: true,
          subscription: { id: 'sub_123' },
        };

        SubscriptionUtils.checkActiveSubscription = jest.fn().mockResolvedValue(mockSubscription);

        const result = await FeatureGatingService.recordFeatureUsage(mockUserId, mockFeatureType);

        expect(result).toEqual({
          recorded: false,
          reason: 'unlimited_access',
          accessType: 'subscription',
        });
      });

      it('should record usage for free users', async () => {
        const mockSubscription = {
          hasActiveSubscription: false,
          subscription: null,
        };

        const mockUpdatedUsage = {
          usageCount: 1,
          usageLimit: 1,
          remainingUsage: 0,
          status: FeatureUsageStatus.EXHAUSTED,
          isExhausted: true,
        };

        SubscriptionUtils.checkActiveSubscription = jest.fn().mockResolvedValue(mockSubscription);
        UserFeatureUsageModel.recordUsage = jest.fn().mockResolvedValue(mockUpdatedUsage);

        const result = await FeatureGatingService.recordFeatureUsage(mockUserId, mockFeatureType);

        expect(result).toEqual({
          recorded: true,
          accessType: 'free',
          ...mockUpdatedUsage,
        });
      });
    });
  });

  describe('SubscriptionUtils', () => {
    describe('checkActiveSubscription', () => {
      it('should return subscription info for active subscription', async () => {
        // This would be mocked in a real test environment
        // Mock implementation would check subscription model
        expect(true).toBe(true); // Placeholder
      });

      it('should return no subscription for free users', async () => {
        // This would be mocked in a real test environment
        expect(true).toBe(true); // Placeholder
      });
    });
  });
});
