import asyncHandler from '../utils/asyncHandler.js';
import { HttpStatus } from '../constants/httpStatus.js';
import HealthChartService from '../services/healthChart.service.js';
import successResponse from '../utils/successResponse.js';

/**
 * Create a health chart (Coach only)
 * @route POST /api/coaches/health-charts
 * @access Coach
 */
const createHealthChart = asyncHandler(async (req, res) => {
  const healthChartData = await HealthChartService.createHealthChart(req);

  successResponse(res, HttpStatus.STATUS_CODE.CREATED, 'Health chart created successfully', healthChartData);
});

/**
 * Publish a health chart (Coach only)
 * @route PATCH /api/coaches/health-charts/:chartId/publish
 * @access Coach
 */
const publishHealthChart = asyncHandler(async (req, res) => {
  const result = await HealthChartService.publishHealthChart(req);

  successResponse(res, HttpStatus.STATUS_CODE.OK, result.message, result);
});

/**
 * Get coach's health charts
 * @route GET /api/coaches/health-charts
 * @access Coach
 */
const getCoachHealthCharts = asyncHandler(async (req, res) => {
  const data = await HealthChartService.getCoachHealthCharts(req);

  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Health charts retrieved successfully', data);
});

/**
 * Create a free advice chart for a client (Coach only)
 * @route POST /api/coaches/health-charts/free-advice
 * @access Coach
 */
const createFreeAdviceChart = asyncHandler(async (req, res) => {
  const result = await HealthChartService.createFreeAdviceChart(req);

  successResponse(res, HttpStatus.STATUS_CODE.CREATED, result.message, result);
});

/**
 * Get health charts for a client
 * @route GET /api/clients/health-charts
 * @access Client
 */
const getClientHealthCharts = asyncHandler(async (req, res) => {
  const data = await HealthChartService.getClientHealthCharts(req);

  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Health charts retrieved successfully', data);
});

/**
 * Get a specific health chart by ID (Client)
 * @route GET /api/clients/health-charts/:chartId
 * @access Client
 */
const getHealthChartById = asyncHandler(async (req, res) => {
  const healthChartData = await HealthChartService.getHealthChartById(req);

  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Health chart retrieved successfully', healthChartData);
});

/**
 * Get client's health chart access summary
 * @route GET /api/clients/health-charts/access-summary
 * @access Client
 */
const getHealthChartAccessSummary = asyncHandler(async (req, res) => {
  const userId = req.user.id || req.user._id;
  const accessInfo = await HealthChartService.checkHealthChartAccess(userId);

  const summary = {
    hasActiveSubscription: accessInfo.unlimited,
    accessType: accessInfo.accessType,
    canAccessFreeChart: accessInfo.hasAccess,
    usageInfo: accessInfo.usageInfo || null,
  };

  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Health chart access summary retrieved successfully', summary);
});

export {
  // Coach endpoints
  createHealthChart,
  publishHealthChart,
  getCoachHealthCharts,
  createFreeAdviceChart,
  
  // Client endpoints
  getClientHealthCharts,
  getHealthChartById,
  getHealthChartAccessSummary,
};
