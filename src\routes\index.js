import authRoutes from './auth.routes.js';
import healthRoutes from './health.routes.js';
import staticRoutes from './static.routes.js';
import publicRoutes from './public/index.js';
import clientRoutes from './client/index.js';
import coachRoutes from './coach/index.js';
import adminRoutes from './admin/index.js';

const registerRoutes = (app) => {
  // Authentication & Authorization
  app.use('/api/auth', authRoutes);

  // Health Check & Monitoring
  app.use('/', healthRoutes);

  // Static Pages & UI Routes
  app.use('/', staticRoutes);

  // Public information (no authentication required)
  app.use('/api', publicRoutes);

  // Client-specific functionality (requires client authentication)
  app.use('/api/clients', clientRoutes);

  // Coach-specific functionality (requires coach authentication)
  app.use('/api/coaches', coachRoutes);

  // Admin-specific functionality (requires admin authentication)
  app.use('/api/admin', adminRoutes);
};

export default registerRoutes;
