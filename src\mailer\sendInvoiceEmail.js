import transporter from '../config/email.config.js';
import config from '../config/environment.config.js';
import invoiceEmailTemplate from './template/invoiceEmailTemplate.js';

const sendInvoiceEmail = async (user, order, paymentDetails) => {
  const emailHtml = invoiceEmailTemplate({
    user,
    order,
    paymentDetails,
    coupon: order.metadata?.appliedCoupon || null,
    originalAmount: order.metadata?.originalAmount || order.amount,
  });

  await transporter.sendMail({
    from: config.email_from,
    to: user.email,
    subject: 'Food For Soul – Payment Confirmation',
    html: emailHtml,
  });
};

export default sendInvoiceEmail;
