import mongoose from 'mongoose';

/**
 * Coach <PERSON><PERSON><PERSON>
 * Represents coaches in the system who can provide health charts and consultations
 */
const CoachSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      unique: true,
    },
    specializations: [
      {
        type: String,
        trim: true,
      },
    ],
    experience: {
      type: Number, // Years of experience
      default: 0,
      min: 0,
    },
    certifications: [
      {
        name: String,
        issuedBy: String,
        issuedDate: Date,
        expiryDate: Date,
        certificateUrl: String,
      },
    ],
    bio: {
      type: String,
      trim: true,
      maxlength: 1000,
    },
    rating: {
      type: Number,
      default: 0,
      min: 0,
      max: 5,
    },
    totalReviews: {
      type: Number,
      default: 0,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isVerified: {
      type: Boolean,
      default: false,
    },
    // Availability settings
    availability: {
      timezone: {
        type: String,
        default: 'UTC',
      },
      workingHours: {
        monday: { start: String, end: String, available: Boolean },
        tuesday: { start: String, end: String, available: Bo<PERSON>an },
        wednesday: { start: String, end: String, available: Boolean },
        thursday: { start: String, end: String, available: Boolean },
        friday: { start: String, end: String, available: Boolean },
        saturday: { start: String, end: String, available: Boolean },
        sunday: { start: String, end: String, available: Boolean },
      },
    },
    // Statistics
    stats: {
      totalClients: {
        type: Number,
        default: 0,
      },
      totalChartsSent: {
        type: Number,
        default: 0,
      },
      totalCallsCompleted: {
        type: Number,
        default: 0,
      },
    },
    metadata: {
      type: Object,
      default: {},
    },
  },
  {
    timestamps: true,
  }
);

// Indexes for optimized querying
CoachSchema.index({ userId: 1 });
CoachSchema.index({ isActive: 1 });
CoachSchema.index({ isVerified: 1 });
CoachSchema.index({ specializations: 1 });
CoachSchema.index({ rating: -1 });

/**
 * Instance method to increment charts sent count
 */
CoachSchema.methods.incrementChartsSent = async function () {
  this.stats.totalChartsSent += 1;
  return this.save();
};

/**
 * Instance method to increment calls completed count
 */
CoachSchema.methods.incrementCallsCompleted = async function () {
  this.stats.totalCallsCompleted += 1;
  return this.save();
};

/**
 * Instance method to update rating
 * @param {number} newRating - New rating to add
 */
CoachSchema.methods.updateRating = async function (newRating) {
  const totalRating = this.rating * this.totalReviews + newRating;
  this.totalReviews += 1;
  this.rating = totalRating / this.totalReviews;
  return this.save();
};

/**
 * Static method to get active coaches
 * @returns {Promise<Array>} Array of active coaches
 */
CoachSchema.statics.getActiveCoaches = async function () {
  return this.find({ isActive: true, isVerified: true })
    .populate('userId', 'firstName lastName email phoneNumber')
    .sort({ rating: -1, totalReviews: -1 });
};

/**
 * Static method to get coaches by specialization
 * @param {string} specialization - Specialization to filter by
 * @returns {Promise<Array>} Array of coaches with the specialization
 */
CoachSchema.statics.getCoachesBySpecialization = async function (specialization) {
  return this.find({
    isActive: true,
    isVerified: true,
    specializations: { $in: [specialization] },
  })
    .populate('userId', 'firstName lastName email phoneNumber')
    .sort({ rating: -1, totalReviews: -1 });
};

export default mongoose.models.Coach || mongoose.model('Coach', CoachSchema, 'coaches');
