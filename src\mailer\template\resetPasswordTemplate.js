import emailFooter from '../emailFooter.js';

export default function resetPasswordTemplate(user, otp) {
  return `
    <!DOCTYPE html>
    <html lang="en">
      <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Password Reset</title>
      </head>
      <body style="margin: 0; padding: 0; background-color: #f9f9f9;">
        <table cellpadding="0" cellspacing="0" width="100%">
          <tr>
            <td align="center" style="padding: 40px 20px;">
              <table cellpadding="0" cellspacing="0" width="600" style="background: #ffffff; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); padding: 40px;">
                <tr>
                  <td align="center" style="padding-bottom: 20px;">
                    <!-- Optional Logo -->
                    <!-- <img src="https://yourdomain.com/logo.png" alt="Company Logo" width="120" /> -->
                    <h1 style="color: #333; font-size: 24px; margin: 0;">Reset Your Password</h1>
                  </td>
                </tr>
                <tr>
                  <td style="font-size: 16px; color: #555; line-height: 1.6;">
                    <p>Dear ${`${user.firstName} ${user.lastName}`},</p>
                    <p>Your one-time password (OTP) for resetting your password is:</p>
                    <h2 style="color:rgb(245, 6, 6); background: #f1f1f1; display: inline-block; padding: 10px 20px; border-radius: 4px;">${otp}</h2>
                    <p>This OTP is valid for <strong>10 minutes</strong>.</p>
                    <p>If you did not request this password reset, please ignore this email.</p>
                  </td>
                </tr>
                ${emailFooter()}
              </table>
            </td>
          </tr>
        </table>
      </body>
    </html>`;
}
