import dotenv from 'dotenv';
dotenv.config();

const config = {
  port: process.env.PORT || 8000, // Application port fallback to 8000 if not set
  env: process.env.NODE_ENV, // Environment (development, production, etc.)

  // SMTP credentials for email services
  smtp_host: process.env.SMTP_HOST,
  smtp_port: process.env.SMTP_PORT,
  smtp_user: process.env.SMTP_USER,
  smtp_pass: process.env.SMTP_PASS,
  email_from: process.env.EMAIL_FROM, // Default sender email address

  // Database connection details
  database_url: process.env.DATABASE_URL,
  database_name: process.env.DATABASE_NAME,

  // JWT configuration for authentication tokens
  jwt_access_token_secret_key: process.env.JWT_ACCESS_TOKEN_SECRET_KEY,
  jwt_refresh_token_secret_key: process.env.JWT_REFRESH_TOKEN_SECRET_KEY,
  jwt_algorithm: process.env.JWT_ALGORITHM || 'HS256', // Default algorithm if not specified

  // Frontend host URL, useful for CORS and email links
  frontend_host: process.env.FRONTEND_HOST,
  allowed_origins: process.env.ALLOWED_ORIGINS,

  // Security: bcrypt salt rounds for password hashing (ensure this is a number)
  salt: parseInt(process.env.SALT_ROUNDS, 10) || 10,

  // Razorpay API credentials for payment integration
  razorpay_key_id: process.env.RAZORPAY_KEY_ID,
  razorpay_key_secret: process.env.RAZORPAY_KEY_SECRET,
  razorpay_webhook_secret: process.env.RAZORPAY_WEBHOOK_SECRET || process.env.RAZORPAY_KEY_SECRET,
};

export default config;
