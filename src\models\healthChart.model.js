import mongoose from 'mongoose';
import { HealthChartType, HealthChartAccessStatus } from '../constants/enums.js';

/**
 * Health Chart Schema
 * Represents health charts that coaches can send to clients
 * Includes access control based on subscription status
 */
const HealthChartSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: true,
      trim: true,
      maxlength: 200,
    },
    description: {
      type: String,
      trim: true,
      maxlength: 1000,
    },
    chartType: {
      type: String,
      enum: HealthChartType.ALL,
      required: true,
      index: true,
    },
    content: {
      // Chart content can be structured data
      chartData: {
        type: Object,
        default: {},
      },
      // Text-based advice or instructions
      textContent: {
        type: String,
        trim: true,
      },
      // File attachments (URLs or file paths)
      attachments: [
        {
          fileName: String,
          fileUrl: String,
          fileType: String,
          fileSize: Number,
          uploadedAt: {
            type: Date,
            default: Date.now,
          },
        },
      ],
      // Structured recommendations
      recommendations: [
        {
          category: String, // e.g., 'nutrition', 'exercise', 'lifestyle'
          recommendation: String,
          priority: {
            type: String,
            enum: ['low', 'medium', 'high'],
            default: 'medium',
          },
        },
      ],
    },
    // Coach who created the chart
    coachId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Coach',
      required: true,
      index: true,
    },
    // Client who receives the chart
    clientId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Client',
      required: true,
      index: true,
    },
    // User ID of the client (for easier querying)
    clientUserId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true,
    },
    // Access control
    accessStatus: {
      type: String,
      enum: HealthChartAccessStatus.ALL,
      default: HealthChartAccessStatus.ACCESSIBLE,
      index: true,
    },
    // Whether this chart requires subscription to access
    requiresSubscription: {
      type: Boolean,
      default: false,
      index: true,
    },
    // Free chart flag (for one-time free advice chart)
    isFreeChart: {
      type: Boolean,
      default: false,
      index: true,
    },
    // Chart visibility and status
    isActive: {
      type: Boolean,
      default: true,
      index: true,
    },
    isPublished: {
      type: Boolean,
      default: false,
    },
    publishedAt: {
      type: Date,
      default: null,
    },
    // Expiration for time-limited charts
    expiresAt: {
      type: Date,
      default: null,
      index: true,
    },
    // Tracking
    viewCount: {
      type: Number,
      default: 0,
    },
    lastViewedAt: {
      type: Date,
      default: null,
    },
    // Tags for categorization
    tags: [
      {
        type: String,
        trim: true,
      },
    ],
    // Priority level
    priority: {
      type: String,
      enum: ['low', 'medium', 'high', 'urgent'],
      default: 'medium',
    },
    // Metadata for additional information
    metadata: {
      type: Object,
      default: {},
    },
  },
  {
    timestamps: true,
  }
);

// Compound indexes for efficient querying
HealthChartSchema.index({ clientUserId: 1, chartType: 1 });
HealthChartSchema.index({ clientUserId: 1, accessStatus: 1 });
HealthChartSchema.index({ coachId: 1, clientId: 1 });
HealthChartSchema.index({ clientUserId: 1, isActive: 1, isPublished: 1 });
HealthChartSchema.index({ requiresSubscription: 1, isFreeChart: 1 });
HealthChartSchema.index({ expiresAt: 1 }, { sparse: true });

/**
 * Instance method to check if chart is accessible to user
 * @param {boolean} hasActiveSubscription - Whether user has active subscription
 * @returns {boolean} True if chart is accessible
 */
HealthChartSchema.methods.isAccessible = function (hasActiveSubscription = false) {
  // Check if chart is active and published
  if (!this.isActive || !this.isPublished) {
    return false;
  }

  // Check expiration
  if (this.expiresAt && new Date() > this.expiresAt) {
    return false;
  }

  // Check access status
  if (this.accessStatus === HealthChartAccessStatus.RESTRICTED) {
    return false;
  }

  // If chart requires subscription and user doesn't have one
  if (this.requiresSubscription && !hasActiveSubscription) {
    return false;
  }

  return true;
};

/**
 * Instance method to increment view count
 */
HealthChartSchema.methods.incrementViewCount = async function () {
  this.viewCount += 1;
  this.lastViewedAt = new Date();
  return this.save();
};

/**
 * Instance method to publish the chart
 */
HealthChartSchema.methods.publish = async function () {
  this.isPublished = true;
  this.publishedAt = new Date();
  return this.save();
};

/**
 * Instance method to expire the chart
 */
HealthChartSchema.methods.expire = async function () {
  this.accessStatus = HealthChartAccessStatus.EXPIRED;
  this.isActive = false;
  return this.save();
};

/**
 * Static method to get accessible charts for a user
 * @param {string} clientUserId - Client user ID
 * @param {boolean} hasActiveSubscription - Whether user has active subscription
 * @param {Object} filters - Additional filters
 * @returns {Promise<Array>} Array of accessible charts
 */
HealthChartSchema.statics.getAccessibleCharts = async function (
  clientUserId,
  hasActiveSubscription = false,
  filters = {}
) {
  const query = {
    clientUserId,
    isActive: true,
    isPublished: true,
    $or: [
      { expiresAt: null },
      { expiresAt: { $gt: new Date() } },
    ],
    accessStatus: { $ne: HealthChartAccessStatus.RESTRICTED },
  };

  // Add subscription filter if user doesn't have subscription
  if (!hasActiveSubscription) {
    query.requiresSubscription = false;
  }

  // Add additional filters
  Object.assign(query, filters);

  return this.find(query)
    .populate('coachId', 'firstName lastName email')
    .sort({ priority: -1, createdAt: -1 });
};

/**
 * Static method to get charts by type for a user
 * @param {string} clientUserId - Client user ID
 * @param {string} chartType - Chart type
 * @param {boolean} hasActiveSubscription - Whether user has active subscription
 * @returns {Promise<Array>} Array of charts
 */
HealthChartSchema.statics.getChartsByType = async function (
  clientUserId,
  chartType,
  hasActiveSubscription = false
) {
  return this.getAccessibleCharts(clientUserId, hasActiveSubscription, { chartType });
};

/**
 * Static method to get free advice chart for a user
 * @param {string} clientUserId - Client user ID
 * @returns {Promise<Object|null>} Free advice chart or null
 */
HealthChartSchema.statics.getFreeAdviceChart = async function (clientUserId) {
  return this.findOne({
    clientUserId,
    isFreeChart: true,
    chartType: HealthChartType.ADVICE_CHART,
    isActive: true,
    isPublished: true,
  }).populate('coachId', 'firstName lastName email');
};

/**
 * Static method to create a free advice chart
 * @param {Object} chartData - Chart data
 * @returns {Promise<Object>} Created chart
 */
HealthChartSchema.statics.createFreeAdviceChart = async function (chartData) {
  const chart = new this({
    ...chartData,
    chartType: HealthChartType.ADVICE_CHART,
    isFreeChart: true,
    requiresSubscription: false,
    accessStatus: HealthChartAccessStatus.ACCESSIBLE,
  });

  return chart.save();
};

/**
 * Static method to get chart statistics for admin
 * @returns {Promise<Object>} Chart statistics
 */
HealthChartSchema.statics.getChartStatistics = async function () {
  const stats = await this.aggregate([
    {
      $group: {
        _id: '$chartType',
        totalCharts: { $sum: 1 },
        activeCharts: { $sum: { $cond: ['$isActive', 1, 0] } },
        publishedCharts: { $sum: { $cond: ['$isPublished', 1, 0] } },
        freeCharts: { $sum: { $cond: ['$isFreeChart', 1, 0] } },
        totalViews: { $sum: '$viewCount' },
        averageViews: { $avg: '$viewCount' },
      },
    },
  ]);

  return stats;
};

// Pre-save middleware to set default values
HealthChartSchema.pre('save', function (next) {
  // Set access status based on subscription requirement
  if (this.isModified('requiresSubscription')) {
    if (this.requiresSubscription && !this.isFreeChart) {
      this.accessStatus = HealthChartAccessStatus.SUBSCRIPTION_REQUIRED;
    } else {
      this.accessStatus = HealthChartAccessStatus.ACCESSIBLE;
    }
  }

  next();
});

export default mongoose.models.HealthChart || 
  mongoose.model('HealthChart', HealthChartSchema, 'health_charts');
