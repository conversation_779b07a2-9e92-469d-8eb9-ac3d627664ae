import SubscriptionService from '../../services/order/subscription.service.js';
import asyncHandler from '../../utils/asyncHandler.js';
import { HttpStatus } from '../../constants/httpStatus.js';
import successResponse from '../../utils/successResponse.js';

/**
 * Get user's active subscription
 * @route GET /api/subscriptions/active
 * @access Client
 */
const getActiveSubscription = asyncHandler(async (req, res) => {
  const subscriptionData = await SubscriptionService.getActiveSubscription(req);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Active subscription retrieved successfully', subscriptionData);
});

/**
 * Get user's subscription history
 * @route GET /api/subscriptions/history
 * @access Client
 */
const getSubscriptionHistory = asyncHandler(async (req, res) => {
  const historyData = await SubscriptionService.getSubscriptionHistory(req);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Subscription history retrieved successfully', historyData);
});

/**
 * Get subscription details by ID
 * @route GET /api/subscriptions/:subscriptionId
 * @access Client
 */
const getSubscriptionById = asyncHandler(async (req, res) => {
  const subscriptionData = await SubscriptionService.getSubscriptionById(req);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Subscription details retrieved successfully', subscriptionData);
});

/**
 * Cancel user's active subscription
 * @route POST /api/subscriptions/cancel
 * @access Client
 */
const cancelSubscription = asyncHandler(async (req, res) => {
  const result = await SubscriptionService.cancelSubscription(req);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Subscription cancelled successfully', result);
});

// Admin Controllers

/**
 * Get all subscriptions (Admin)
 * @route GET /api/admin/subscriptions
 * @access Admin
 */
const getAllSubscriptions = asyncHandler(async (req, res) => {
  const subscriptionsData = await SubscriptionService.getAllSubscriptions(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Subscriptions retrieved successfully', subscriptionsData);
});

/**
 * Get subscription statistics (Admin)
 * @route GET /api/admin/subscriptions/stats
 * @access Admin
 */
const getSubscriptionStats = asyncHandler(async (req, res) => {
  const statsData = await SubscriptionService.getSubscriptionStats();
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Subscription statistics retrieved successfully', statsData);
});

/**
 * Toggle subscription status (Admin)
 * @route PATCH /api/admin/subscriptions/:subscriptionId/toggle
 * @access Admin
 */
const toggleSubscriptionStatus = asyncHandler(async (req, res) => {
  const { message, subscription } = await SubscriptionService.toggleSubscriptionStatus(req);

  successResponse(res, HttpStatus.STATUS_CODE.OK, message, subscription);
});

/**
 * Get subscription by ID (Admin)
 * @route GET /api/admin/subscriptions/:subscriptionId
 * @access Admin
 */
const getSubscriptionByIdAdmin = asyncHandler(async (req, res) => {
  const subscriptionData = await SubscriptionService.getSubscriptionByIdAdmin(req);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Subscription details retrieved successfully', subscriptionData);
});

/**
 * Update expired subscriptions (Admin/System)
 * @route POST /api/admin/subscriptions/update-expired
 * @access Admin
 */
const updateExpiredSubscriptions = asyncHandler(async (req, res) => {
  const result = await SubscriptionService.updateExpiredSubscriptions();
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Expired subscriptions updated successfully', result);
});

export {
  // Client controllers
  getActiveSubscription,
  getSubscriptionHistory,
  getSubscriptionById,
  cancelSubscription,

  // Admin controllers
  getAllSubscriptions,
  getSubscriptionStats,
  toggleSubscriptionStatus,
  getSubscriptionByIdAdmin,
  updateExpiredSubscriptions,
};
