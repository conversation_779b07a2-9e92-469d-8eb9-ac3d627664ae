import asyncHandler from '../utils/asyncHandler.js';
import { HttpStatus } from '../constants/httpStatus.js';
import NotificationService from '../services/notification.service.js';
import OnboardingService from '../services/onboarding.service.js';
import successResponse from '../utils/successResponse.js';

/**
 * Get user notifications
 * @route GET /api/clients/notifications
 * @access Client
 */
const getUserNotifications = asyncHandler(async (req, res) => {
  const userId = req.user.id || req.user._id;
  const notifications = await NotificationService.getUserNotifications(userId);

  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Notifications retrieved successfully', notifications);
});

/**
 * Get notification summary
 * @route GET /api/clients/notifications/summary
 * @access Client
 */
const getNotificationSummary = asyncHandler(async (req, res) => {
  const userId = req.user.id || req.user._id;
  const summary = await NotificationService.getNotificationSummary(userId);

  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Notification summary retrieved successfully', summary);
});

/**
 * Get feature-specific notifications
 * @route GET /api/clients/notifications/feature/:featureType
 * @access Client
 */
const getFeatureNotifications = asyncHandler(async (req, res) => {
  const userId = req.user.id || req.user._id;
  const { featureType } = req.params;

  const notifications = await NotificationService.getFeatureNotifications(userId, featureType);

  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Feature notifications retrieved successfully', notifications);
});

/**
 * Get upgrade prompts
 * @route GET /api/clients/notifications/upgrade-prompts
 * @access Client
 */
const getUpgradePrompts = asyncHandler(async (req, res) => {
  const userId = req.user.id || req.user._id;
  const prompts = await NotificationService.getUpgradePrompts(userId);

  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Upgrade prompts retrieved successfully', prompts);
});

/**
 * Mark notification as read
 * @route PATCH /api/clients/notifications/:notificationId/read
 * @access Client
 */
const markNotificationAsRead = asyncHandler(async (req, res) => {
  const userId = req.user.id || req.user._id;
  const { notificationId } = req.params;

  const result = await NotificationService.markNotificationAsRead(userId, notificationId);

  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Notification marked as read', result);
});

/**
 * Get onboarding information
 * @route GET /api/clients/onboarding
 * @access Client
 */
const getOnboardingInfo = asyncHandler(async (req, res) => {
  const userId = req.user.id || req.user._id;
  const onboardingInfo = await OnboardingService.initializeUserOnboarding(userId);

  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Onboarding information retrieved successfully', onboardingInfo);
});

/**
 * Get onboarding steps
 * @route GET /api/clients/onboarding/steps
 * @access Client
 */
const getOnboardingSteps = asyncHandler(async (req, res) => {
  const userId = req.user.id || req.user._id;
  const steps = await OnboardingService.getOnboardingSteps(userId);

  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Onboarding steps retrieved successfully', steps);
});

/**
 * Get onboarding progress
 * @route GET /api/clients/onboarding/progress
 * @access Client
 */
const getOnboardingProgress = asyncHandler(async (req, res) => {
  const userId = req.user.id || req.user._id;
  const progress = await OnboardingService.getOnboardingProgress(userId);

  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Onboarding progress retrieved successfully', progress);
});

/**
 * Complete onboarding step
 * @route POST /api/clients/onboarding/steps/:stepId/complete
 * @access Client
 */
const completeOnboardingStep = asyncHandler(async (req, res) => {
  const userId = req.user.id || req.user._id;
  const { stepId } = req.params;
  const stepData = req.body;

  const result = await OnboardingService.completeOnboardingStep(userId, stepId, stepData);

  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Onboarding step completed successfully', result);
});

export {
  getUserNotifications,
  getNotificationSummary,
  getFeatureNotifications,
  getUpgradePrompts,
  markNotificationAsRead,
  getOnboardingInfo,
  getOnboardingSteps,
  getOnboardingProgress,
  completeOnboardingStep,
};
