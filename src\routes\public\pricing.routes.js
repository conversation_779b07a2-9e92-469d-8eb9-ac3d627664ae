import express from 'express';
import { validateMongoId } from '../../middlewares/validateMongoId.js';
import { getPricingPlanById, getPricingPlans } from '../../controllers/pricing/pricingPlans.controller.js';

const router = express.Router();

/**
 * @route   GET /api/pricing-plans
 * @desc    Get all pricing plans for public viewing
 * @access  Public
 */
router.get('/', getPricingPlans);

/**
 * @route   GET /api/pricing-plans/:id
 * @desc    Get a specific pricing plan by ID for public viewing
 * @access  Public
 */
router.get('/:id', validateMongoId(), getPricingPlanById);

export default router;
