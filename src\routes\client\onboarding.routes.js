import express from 'express';
import validateAccessToken from '../../middlewares/validateAccessToken.js';
import authenticateJWT from '../../middlewares/authenticateJWT.js';
import authorizeRoles from '../../middlewares/authorizeRoles.js';
import { UserRoles } from '../../constants/userRoles.js';
import {
  getOnboardingInfo,
  getOnboardingSteps,
  getOnboardingProgress,
  completeOnboardingStep,
} from '../../controllers/notification.controller.js';

const router = express.Router();

// Apply global middlewares: JWT + Role-Based Authorization for Clients
router.use(validateAccessToken, authenticateJWT, authorizeRoles(UserRoles.CLIENT));

/**
 * @route   GET /api/clients/onboarding
 * @desc    Get complete onboarding information for the user
 * @access  Client only
 */
router.get('/', getOnboardingInfo);

/**
 * @route   GET /api/clients/onboarding/steps
 * @desc    Get onboarding steps for the user
 * @access  Client only
 */
router.get('/steps', getOnboardingSteps);

/**
 * @route   GET /api/clients/onboarding/progress
 * @desc    Get onboarding progress for the user
 * @access  Client only
 */
router.get('/progress', getOnboardingProgress);

/**
 * @route   POST /api/clients/onboarding/steps/:stepId/complete
 * @desc    Complete an onboarding step
 * @access  Client only
 */
router.post('/steps/:stepId/complete', completeOnboardingStep);

export default router;
