import emailFooter from '../emailFooter.js';

const bookCoachCallTemplate = (user, coachCallRequest) => {
  return `
  <!DOCTYPE html>
  <html lang="en">
    <head>
      <meta charset="UTF-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <title>Coach Call Request</title>
    </head>
    <body style="margin: 0; padding: 0; background-color: #f9f9f9;">
      <table cellpadding="0" cellspacing="0" width="100%">
        <tr>
          <td align="center" style="padding: 40px 20px;">
            <table cellpadding="0" cellspacing="0" width="600" style="background: #ffffff; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); padding: 40px;">
              <tr>
                <td align="center" style="padding-bottom: 20px;">
                  <h1 style="color: #333; font-size: 24px; margin: 0;">Coach Call Request</h1>
                </td>
              </tr>
              <tr>
                <td style="font-size: 16px; color: #555; line-height: 1.6;">
                  <p>Dear ${`${user.firstName} ${user.lastName}`},</p>
                  <p>Thank you for requesting a coach call. Your request has been received and is being processed.</p>
                  <p>We will get back to you soon with further instructions.</p>
                </td>
              </tr>
              ${emailFooter()}
            </table>
          </td>
        </tr>
      </table>
    </body>
  </html>`;
};

export default bookCoachCallTemplate;
